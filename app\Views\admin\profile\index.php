<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-bold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="text-primary"><?= esc($breadcrumb['name']) ?></li>
                <?php else: ?>
                    <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= esc($breadcrumb['name']) ?></a></li>
                    <li class="text-gray-500">/</li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
</div>

<!-- Profile Card -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Profile Information
        </h3>
    </div>
    
    <div class="p-7">
        <div class="mb-8 flex flex-col gap-6 xl:flex-row">
            <!-- Profile Picture -->
            <div class="w-full xl:w-1/3">
                <div class="text-center">
                    <div class="mx-auto mb-4 h-32 w-32 rounded-full overflow-hidden border-4 border-primary">
                        <img src="<?= esc($user_avatar_url) ?>" 
                             alt="<?= esc($user_display_name) ?>" 
                             class="h-full w-full object-cover">
                    </div>
                    <h4 class="mb-1.5 text-2xl font-semibold text-black dark:text-white">
                        <?= esc($user_display_name) ?>
                    </h4>
                    <p class="font-medium text-gray-600 dark:text-gray-400">
                        <?= esc($user_role_name) ?>
                    </p>
                    
                    <!-- Action Buttons -->
                    <div class="mt-6 flex flex-col gap-3 sm:flex-row sm:justify-center">
                        <a href="<?= base_url('admin/profile/edit') ?>" 
                           class="inline-flex items-center justify-center rounded-md bg-primary py-2 px-6 text-center font-medium text-white hover:bg-opacity-90 lg:px-8 xl:px-10">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Profile
                        </a>
                        <a href="<?= base_url('admin/profile/change-password') ?>" 
                           class="inline-flex items-center justify-center rounded-md border border-primary py-2 px-6 text-center font-medium text-primary hover:bg-opacity-90 lg:px-8 xl:px-10">
                            <i class="fas fa-key mr-2"></i>
                            Change Password
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Profile Details -->
            <div class="w-full xl:w-2/3">
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- Personal Information -->
                    <div class="col-span-2">
                        <h5 class="mb-4 text-lg font-semibold text-black dark:text-white">
                            Personal Information
                        </h5>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            First Name
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <?= esc($user->first_name ?? 'Not provided') ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Last Name
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <?= esc($user->last_name ?? 'Not provided') ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Username
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <?= esc($user->username ?? 'Not provided') ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Email Address
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <?= esc($user->email ?? 'Not provided') ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Phone Number
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <?= esc($user->phone ?? 'Not provided') ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Account Status
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <span class="inline-flex rounded-full <?= $user->active ? 'bg-success bg-opacity-10 text-success' : 'bg-danger bg-opacity-10 text-danger' ?> py-1 px-3 text-sm font-medium">
                                <?= $user->active ? 'Active' : 'Inactive' ?>
                            </span>
                        </div>
                    </div>
                    
                    <!-- Account Information -->
                    <div class="col-span-2 mt-6">
                        <h5 class="mb-4 text-lg font-semibold text-black dark:text-white">
                            Account Information
                        </h5>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            User Roles
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <?php if (!empty($groups)): ?>
                                <?php foreach ($groups as $group): ?>
                                    <span class="inline-flex rounded-full bg-primary bg-opacity-10 text-primary py-1 px-3 text-sm font-medium mr-2 mb-2">
                                        <?= esc(ucfirst($group)) ?>
                                    </span>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <span class="text-gray-500">No roles assigned</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Member Since
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <?= date('F j, Y', strtotime($user->created_at)) ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Last Updated
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <?= date('F j, Y g:i A', strtotime($user->updated_at)) ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                            Email Verified
                        </label>
                        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                            <span class="inline-flex rounded-full <?= !empty($user->email_verified_at) ? 'bg-success bg-opacity-10 text-success' : 'bg-warning bg-opacity-10 text-warning' ?> py-1 px-3 text-sm font-medium">
                                <?= !empty($user->email_verified_at) ? 'Verified' : 'Not Verified' ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
