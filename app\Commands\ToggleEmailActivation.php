<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class ToggleEmailActivation extends BaseCommand
{
    protected $group       = 'Authentication';
    protected $name        = 'auth:toggle-email';
    protected $description = 'Toggle email activation requirement for new registrations';

    public function run(array $params)
    {
        $action = $params[0] ?? null;
        
        if (!in_array($action, ['enable', 'disable', 'status'])) {
            CLI::write('Usage: php spark auth:toggle-email [enable|disable|status]', 'yellow');
            CLI::newLine();
            CLI::write('Commands:', 'cyan');
            CLI::write('  enable  - Enable email activation for new registrations', 'white');
            CLI::write('  disable - Disable email activation (auto-activate accounts)', 'white');
            CLI::write('  status  - Show current email activation status', 'white');
            return;
        }

        $configPath = APPPATH . 'Config/Auth.php';
        $configContent = file_get_contents($configPath);

        switch ($action) {
            case 'enable':
                $this->enableEmailActivation($configPath, $configContent);
                break;
            case 'disable':
                $this->disableEmailActivation($configPath, $configContent);
                break;
            case 'status':
                $this->showStatus($configContent);
                break;
        }
    }

    private function enableEmailActivation(string $configPath, string $configContent): void
    {
        $newContent = preg_replace(
            '/public bool \$requireEmailActivation = false;/',
            'public bool $requireEmailActivation = true;',
            $configContent
        );

        if ($newContent !== $configContent) {
            file_put_contents($configPath, $newContent);
            CLI::write('✅ Email activation ENABLED', 'green');
            CLI::write('   New users will need to activate their accounts via email.', 'white');
        } else {
            CLI::write('ℹ️  Email activation is already enabled.', 'yellow');
        }
    }

    private function disableEmailActivation(string $configPath, string $configContent): void
    {
        $newContent = preg_replace(
            '/public bool \$requireEmailActivation = true;/',
            'public bool $requireEmailActivation = false;',
            $configContent
        );

        if ($newContent !== $configContent) {
            file_put_contents($configPath, $newContent);
            CLI::write('✅ Email activation DISABLED', 'green');
            CLI::write('   New users will be automatically activated upon registration.', 'white');
        } else {
            CLI::write('ℹ️  Email activation is already disabled.', 'yellow');
        }
    }

    private function showStatus(string $configContent): void
    {
        CLI::write('📧 Email Activation Status', 'cyan');
        CLI::newLine();

        if (preg_match('/public bool \$requireEmailActivation = true;/', $configContent)) {
            CLI::write('Status: ENABLED ✅', 'green');
            CLI::write('New users must activate their accounts via email.', 'white');
        } elseif (preg_match('/public bool \$requireEmailActivation = false;/', $configContent)) {
            CLI::write('Status: DISABLED ❌', 'red');
            CLI::write('New users are automatically activated upon registration.', 'white');
        } else {
            CLI::write('Status: UNKNOWN ⚠️', 'yellow');
            CLI::write('Could not determine email activation status.', 'white');
        }

        CLI::newLine();
        CLI::write('To change status:', 'cyan');
        CLI::write('  php spark auth:toggle-email enable', 'white');
        CLI::write('  php spark auth:toggle-email disable', 'white');
    }
}
