<?php

namespace App\Controllers;

use CodeIgniter\Shield\Controllers\RegisterController;
use CodeIgniter\Shield\Models\UserModel;
use CodeIgniter\HTTP\RedirectResponse;

class AuthController extends RegisterController
{
    protected $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new UserModel();
    }

    /**
     * Displays the registration form.
     */
    public function registerView(): string
    {
        if (auth()->loggedIn()) {
            return redirect()->to('/admin');
        }

        // Check if registration is allowed
        if (!setting('Auth.allowRegistration')) {
            return redirect()->back()->withInput()->with('error', 'Registration is currently disabled.');
        }

        return view('auth/register');
    }

    /**
     * Attempts to register the user.
     */
    public function registerAction(): RedirectResponse
    {
        if (auth()->loggedIn()) {
            return redirect()->to(config('Auth')->registerRedirect());
        }

        // Check if registration is allowed
        if (!setting('Auth.allowRegistration')) {
            return redirect()->back()->withInput()->with('error', lang('Auth.registerDisabled'));
        }

        $rules = $this->getValidationRules();

        if (!$this->validateData($this->request->getPost(), $rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Save the user
        $allowedPostFields = array_keys($rules);
        $user = $this->getUserEntity();
        $user->fill($this->request->getPost($allowedPostFields));

        // Workaround for email only registration/login
        if ($user->username === null) {
            $user->username = null;
        }

        try {
            $this->userModel->save($user);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }

        // Get the user object with ID
        $user = $this->userModel->findById($this->userModel->getInsertID());

        // Assign role based on form input
        $role = $this->request->getPost('role');
        if (!empty($role) && in_array($role, ['teacher', 'staff', 'parent'])) {
            $user->addGroup($role);
        } else {
            // Default role
            $user->addGroup(setting('AuthGroups.defaultGroup'));
        }

        // Set additional user data
        $additionalData = [
            'first_name' => $this->request->getPost('first_name') ?? '',
            'last_name'  => $this->request->getPost('last_name') ?? '',
            'phone'      => $this->request->getPost('phone') ?? '',
        ];

        if (!empty(array_filter($additionalData))) {
            $this->userModel->update($user->id, $additionalData);
        }

        $user = $user->fresh();

        // Success!
        return $this->completeRegistration($user);
    }

    /**
     * Returns the rules that should be used for validation.
     *
     * @return array<string, array<string, array<string>|string>>
     * @phpstan-return array<string, array<string, string|list<string>>>
     */
    protected function getValidationRules(): array
    {
        $rules = setting('Validation.registration') ?? [
            'username' => [
                'label' => 'Auth.username',
                'rules' => array_merge(
                    config('AuthSession')->usernameValidationRules,
                    [
                        'is_unique[users.username,id,{id}]',
                    ]
                ),
            ],
            'email' => [
                'label' => 'Auth.email',
                'rules' => array_merge(
                    config('AuthSession')->emailValidationRules,
                    [
                        'is_unique[auth_identities.secret,id,{id}]',
                    ]
                ),
            ],
            'password' => [
                'label' => 'Auth.password',
                'rules' => 'required|strong_password',
            ],
            'password_confirm' => [
                'label' => 'Auth.passwordConfirm',
                'rules' => 'required|matches[password]',
            ],
        ];

        // Add role validation
        $rules['role'] = [
            'label' => 'Role',
            'rules' => 'required|in_list[teacher,staff,parent]',
        ];

        // Add optional fields
        $rules['first_name'] = [
            'label' => 'First Name',
            'rules' => 'permit_empty|max_length[100]',
        ];

        $rules['last_name'] = [
            'label' => 'Last Name',
            'rules' => 'permit_empty|max_length[100]',
        ];

        $rules['phone'] = [
            'label' => 'Phone',
            'rules' => 'permit_empty|max_length[20]',
        ];

        $rules['terms'] = [
            'label' => 'Terms and Conditions',
            'rules' => 'required',
        ];

        return $rules;
    }

    /**
     * Complete the registration process
     */
    protected function completeRegistration($user): RedirectResponse
    {
        $user = $user->fresh();

        // Check if email activation is enabled
        $authConfig = config('Auth');
        $emailActivationEnabled = !empty($authConfig->actions['register']);

        if ($emailActivationEnabled) {
            // If email activation is enabled, start the action
            $hasAction = auth()->getProvider()->startUpAction('register', $user);
            if ($hasAction) {
                return redirect()->route('auth-action-show')->with('message',
                    'Registration successful! Please check your email to activate your account.');
            }
        } else {
            // If email activation is disabled, activate user immediately
            $user->activate();
        }

        // Success message
        $message = $emailActivationEnabled
            ? 'Registration successful! Please check your email to activate your account.'
            : 'Registration successful! You can now login to your account.';

        return redirect()->to($authConfig->redirects['register'])->with('message', $message);
    }

    /**
     * Check activation status (for AJAX calls)
     */
    public function checkActivation()
    {
        $user = auth()->user();
        
        if ($user && $user->isActivated()) {
            return $this->response->setJSON(['activated' => true]);
        }

        return $this->response->setJSON(['activated' => false]);
    }
}
