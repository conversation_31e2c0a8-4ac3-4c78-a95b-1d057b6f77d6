<?php

namespace App\Controllers;

use App\Models\StudentSessionModel;
use App\Models\StudentsModel;
use App\Models\ClassesModel;
use App\Models\SectionsModel;
use App\Models\SessionsModel;

class StudentSessionController extends BaseCrudController
{
    protected $studentsModel;
    protected $classesModel;
    protected $sectionsModel;
    protected $sessionsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentSessionModel();
        $this->studentsModel = new StudentsModel();
        $this->classesModel = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        $this->sessionsModel = new SessionsModel();
        
        $this->viewPath = 'admin/student_apps/sessions';
        $this->routePrefix = 'admin/student-apps/sessions';
        $this->entityName = 'Student Session';
        $this->entityNamePlural = 'Student Sessions';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = array_merge([
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'students' => $this->studentsModel->getForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ], $this->getCurrentUserData());

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'students' => $this->studentsModel->getForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown()
        ];
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $filters = [
            'student_id' => $this->request->getPost('student_id'),
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id'),
            'session_id' => $this->request->getPost('session_id')
        ];

        $builder = $this->model->getSessionsWithDetails($filters);

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('students.firstname', $searchValue)
                   ->orLike('students.lastname', $searchValue)
                   ->orLike('students.admission_no', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'firstname', 'admission_no', 'class', 'section', 'session'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->get()->getResultArray();

        $data = [];
        foreach ($records as $record) {
            $actions = $this->getActionButtons($record);

            $data[] = [
                'id' => $record['id'],
                'student_name' => $record['firstname'] . ' ' . $record['lastname'],
                'admission_no' => $record['admission_no'],
                'class' => $record['class'],
                'section' => $record['section'],
                'session' => $record['session'],
                'roll_no' => $record['roll_no'] ?? '-',
                'transport' => $record['transport'] ?? '-',
                'hostel' => $record['hostel'] ?? '-',
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
