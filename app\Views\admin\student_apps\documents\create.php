<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?>Create <?= $entity_name ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h2 class="text-title-md2 font-semibold text-black dark:text-white">
            Create <?= $entity_name ?>
        </h2>
        <nav>
            <ol class="flex items-center gap-2">
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                        <li class="text-body-color">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Form Card -->
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">
                Create New <?= $entity_name ?>
            </h3>
        </div>
        
        <form id="createForm" action="<?= base_url($route_prefix . '/store') ?>" method="POST" enctype="multipart/form-data">
            <?= csrf_field() ?>
            <div class="p-6.5">
                <div class="mb-4.5 flex flex-col gap-6 xl:flex-row">
                    <!-- Student Selection -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Student <span class="text-meta-1">*</span>
                        </label>
                        <select name="student_id" id="student_id" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                            <option value="">Select Student</option>
                            <?php if (isset($students)): ?>
                                <?php foreach ($students as $student): ?>
                                    <option value="<?= $student['id'] ?>"><?= $student['firstname'] . ' ' . $student['lastname'] . ' (' . $student['admission_no'] . ')' ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <!-- Document Title -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Document Title <span class="text-meta-1">*</span>
                        </label>
                        <input type="text" name="title" id="title" placeholder="Enter document title" 
                               class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                    </div>
                </div>



                <!-- Document Files -->
                <div class="mb-4.5">
                    <label class="mb-2.5 block text-black dark:text-white">
                        Document Files <span class="text-meta-1">*</span>
                    </label>
                    <div class="file-upload-area" id="file-upload-area">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <p class="text-lg font-medium text-gray-700 mb-2">Drop your files here or click to browse</p>
                        <p class="text-sm text-gray-500 mb-4">Allowed formats: PDF, DOC, DOCX, JPG, JPEG, PNG (Max: 5MB each)</p>
                        <input type="file" name="doc_files[]" id="doc_files" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                               class="hidden" multiple>
                        <button type="button" id="browse-btn" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition">
                            <i class="fas fa-folder-open mr-2"></i>Browse Files
                        </button>
                        <div class="selected-files" id="selected-files" style="display: none;">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Selected Files:</h4>
                            <div id="files-list" class="space-y-2"></div>
                            <button type="button" id="clear-all-files" class="mt-3 px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition">
                                <i class="fas fa-trash mr-1"></i>Clear All
                            </button>
                        </div>
                    </div>
                </div>



                <!-- Action Buttons -->
                <div class="flex gap-4.5">
                    <button type="submit" class="flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90">
                        <i class="fas fa-save mr-2"></i>
                        Create Document
                    </button>
                    <a href="<?= base_url($route_prefix) ?>" class="flex w-full justify-center rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
.upload-progress-container {
    padding: 1rem;
}

.progress-bar-container {
    position: relative;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#progress-bar {
    position: relative;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.file-upload-area {
    transition: all 0.3s ease;
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background: #f9fafb;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.file-upload-area.dragover {
    border-color: #1d4ed8;
    background: #dbeafe;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.file-info {
    display: none;
    margin-top: 1rem;
    padding: 0.75rem;
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 0.375rem;
    color: #0c4a6e;
}

.selected-files {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: between;
    padding: 0.5rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
}

.file-item:last-child {
    margin-bottom: 0;
}

.file-item-info {
    flex: 1;
    display: flex;
    align-items: center;
}

.file-item-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-item-remove {
    color: #ef4444;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.file-item-remove:hover {
    background-color: #fee2e2;
}

.upload-progress-item {
    margin-top: 0.5rem;
}

.progress-bar-small {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-fill-small {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createForm');
    const fileInput = document.getElementById('doc_files');
    const fileUploadArea = document.getElementById('file-upload-area');
    const browseBtn = document.getElementById('browse-btn');
    const selectedFiles = document.getElementById('selected-files');
    const filesList = document.getElementById('files-list');
    const clearAllBtn = document.getElementById('clear-all-files');

    let selectedFilesArray = [];

    // Browse button click
    browseBtn.addEventListener('click', function() {
        fileInput.click();
    });

    // File upload area click
    fileUploadArea.addEventListener('click', function(e) {
        if (e.target === fileUploadArea || e.target.closest('.upload-icon') || e.target.closest('p')) {
            fileInput.click();
        }
    });

    // Drag and drop functionality
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');

        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            handleMultipleFileSelection(files);
        }
    });

    // File input change
    fileInput.addEventListener('change', function() {
        const files = Array.from(this.files);
        if (files.length > 0) {
            handleMultipleFileSelection(files);
        }
    });

    // Clear all files button
    clearAllBtn.addEventListener('click', function() {
        selectedFilesArray = [];
        fileInput.value = '';
        updateFilesDisplay();
    });

    // Handle multiple file selection
    function handleMultipleFileSelection(files) {
        const validFiles = [];
        const errors = [];

        files.forEach(file => {
            // Validate file size
            const maxSize = 5 * 1024 * 1024; // 5MB
            if (file.size > maxSize) {
                errors.push(`${file.name}: File too large (max 5MB)`);
                return;
            }

            // Validate file type
            const allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                errors.push(`${file.name}: Invalid file type`);
                return;
            }

            // Check for duplicates
            const isDuplicate = selectedFilesArray.some(existingFile =>
                existingFile.name === file.name && existingFile.size === file.size
            );

            if (!isDuplicate) {
                validFiles.push(file);
            } else {
                errors.push(`${file.name}: File already selected`);
            }
        });

        // Add valid files to selection
        selectedFilesArray.push(...validFiles);

        // Show errors if any
        if (errors.length > 0) {
            Swal.fire({
                icon: 'warning',
                title: 'Some Files Could Not Be Added',
                html: errors.join('<br>'),
                confirmButtonText: 'OK'
            });
        }

        // Update display
        updateFilesDisplay();
    }

    // Update files display
    function updateFilesDisplay() {
        if (selectedFilesArray.length === 0) {
            selectedFiles.style.display = 'none';
            // Show upload area elements
            const uploadElements = fileUploadArea.querySelectorAll('.upload-icon, p, button');
            uploadElements.forEach(el => el.style.display = '');
        } else {
            selectedFiles.style.display = 'block';
            // Hide upload area elements
            const uploadElements = fileUploadArea.querySelectorAll('.upload-icon, p, button');
            uploadElements.forEach(el => el.style.display = 'none');

            // Update files list
            filesList.innerHTML = '';
            selectedFilesArray.forEach((file, index) => {
                const fileItem = createFileItem(file, index);
                filesList.appendChild(fileItem);
            });
        }
    }

    // Create file item element
    function createFileItem(file, index) {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
            <div class="file-item-info">
                <i class="fas fa-file mr-2 text-blue-500"></i>
                <div>
                    <div class="font-medium text-sm">${file.name}</div>
                    <div class="text-xs text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                </div>
            </div>
            <div class="file-item-actions">
                <button type="button" class="file-item-remove" onclick="removeFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        return fileItem;
    }

    // Remove individual file
    window.removeFile = function(index) {
        selectedFilesArray.splice(index, 1);
        updateFilesDisplay();
    }
    
    // Form submission with multiple file upload progress
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        console.log('Form submission started');

        // Check if required fields are filled
        const studentId = document.getElementById('student_id').value;
        const title = document.getElementById('title').value;

        if (!studentId) {
            Swal.fire({
                icon: 'warning',
                title: 'Student Required',
                text: 'Please select a student'
            });
            return;
        }

        if (!title) {
            Swal.fire({
                icon: 'warning',
                title: 'Title Required',
                text: 'Please enter a document title'
            });
            return;
        }

        if (selectedFilesArray.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'No Files Selected',
                text: 'Please select at least one file to upload'
            });
            return;
        }

        console.log('Files to upload:', selectedFilesArray.length);

        // Start multiple file upload process
        uploadMultipleFiles(studentId, title);
    });

    // Upload multiple files function
    function uploadMultipleFiles(studentId, title) {
        let currentFileIndex = 0;
        let successCount = 0;
        let errorCount = 0;
        const totalFiles = selectedFilesArray.length;
        const errors = [];

        Swal.fire({
            title: `Uploading ${totalFiles} Document${totalFiles > 1 ? 's' : ''}...`,
            html: `
                <div class="upload-progress-container">
                    <div class="mb-4">
                        <div class="text-sm text-gray-600 mb-2">Overall Progress: <span id="overall-progress">0/${totalFiles}</span></div>
                        <div class="progress-bar-container" style="width: 100%; background-color: #e5e7eb; border-radius: 0.5rem; overflow: hidden;">
                            <div id="overall-progress-bar" style="width: 0%; height: 1rem; background: linear-gradient(90deg, #10b981, #059669); transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="text-sm text-gray-600 mb-2">Current File: <span id="current-file-name">-</span></div>
                        <div class="progress-bar-container" style="width: 100%; background-color: #e5e7eb; border-radius: 0.5rem; overflow: hidden;">
                            <div id="current-progress-bar" style="width: 0%; height: 1rem; background: linear-gradient(90deg, #3b82f6, #1d4ed8); transition: width 0.3s ease; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem;"></div>
                        </div>
                    </div>
                    <div id="upload-status" style="text-align: center; color: #6b7280; font-size: 0.875rem;">Preparing upload...</div>
                </div>
            `,
            allowOutsideClick: false,
            showCancelButton: true,
            cancelButtonText: 'Cancel All',
            cancelButtonColor: '#ef4444',
            showConfirmButton: false,
            didOpen: () => {
                uploadNextFile();
            }
        });

        function uploadNextFile() {
            if (currentFileIndex >= totalFiles) {
                // All files processed
                showFinalResult();
                return;
            }

            const file = selectedFilesArray[currentFileIndex];
            const currentFileNameEl = document.getElementById('current-file-name');
            const currentProgressBar = document.getElementById('current-progress-bar');
            const uploadStatus = document.getElementById('upload-status');

            if (currentFileNameEl) currentFileNameEl.textContent = file.name;
            if (uploadStatus) uploadStatus.textContent = `Uploading ${file.name}...`;
            if (currentProgressBar) {
                currentProgressBar.style.width = '0%';
                currentProgressBar.textContent = '0%';
            }

            // Create form data for this file
            const formData = new FormData();
            formData.append('student_id', studentId);
            formData.append('title', `${title} - ${file.name}`);
            formData.append('doc_file', file);

            // Create XMLHttpRequest for this file
            const xhr = new XMLHttpRequest();

            // Track upload progress
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    const currentProgressBar = document.getElementById('current-progress-bar');

                    if (currentProgressBar) {
                        currentProgressBar.style.width = percentComplete + '%';
                        currentProgressBar.textContent = percentComplete + '%';
                    }
                }
            });

            // Handle completion
            xhr.addEventListener('load', function() {
                try {
                    const data = JSON.parse(xhr.responseText);

                    if (data.success) {
                        successCount++;
                    } else {
                        errorCount++;
                        errors.push(`${file.name}: ${data.message || 'Upload failed'}`);
                    }
                } catch (error) {
                    errorCount++;
                    errors.push(`${file.name}: Invalid response from server`);
                }

                // Update overall progress
                currentFileIndex++;
                updateOverallProgress();

                // Upload next file
                uploadNextFile();
            });

            // Handle errors
            xhr.addEventListener('error', function() {
                errorCount++;
                errors.push(`${file.name}: Network error`);
                currentFileIndex++;
                updateOverallProgress();
                uploadNextFile();
            });

            // Handle timeout
            xhr.addEventListener('timeout', function() {
                errorCount++;
                errors.push(`${file.name}: Upload timeout`);
                currentFileIndex++;
                updateOverallProgress();
                uploadNextFile();
            });

            // Configure and send request
            xhr.open('POST', form.action);
            xhr.timeout = 300000; // 5 minutes timeout
            xhr.send(formData);
        }

        function updateOverallProgress() {
            const overallProgress = document.getElementById('overall-progress');
            const overallProgressBar = document.getElementById('overall-progress-bar');

            if (overallProgress) {
                overallProgress.textContent = `${currentFileIndex}/${totalFiles}`;
            }

            if (overallProgressBar) {
                const percentage = Math.round((currentFileIndex / totalFiles) * 100);
                overallProgressBar.style.width = percentage + '%';
            }
        }

        function showFinalResult() {
            if (errorCount === 0) {
                Swal.fire({
                    icon: 'success',
                    title: 'All Documents Uploaded!',
                    text: `Successfully uploaded ${successCount} document${successCount > 1 ? 's' : ''}`,
                    timer: 3000,
                    timerProgressBar: true
                }).then(() => {
                    window.location.href = '<?= base_url($route_prefix) ?>';
                });
            } else if (successCount === 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Upload Failed!',
                    html: `All uploads failed:<br>${errors.join('<br>')}`
                });
            } else {
                Swal.fire({
                    icon: 'warning',
                    title: 'Partial Success',
                    html: `${successCount} document${successCount > 1 ? 's' : ''} uploaded successfully, ${errorCount} failed:<br>${errors.join('<br>')}`,
                    confirmButtonText: 'Continue'
                }).then(() => {
                    window.location.href = '<?= base_url($route_prefix) ?>';
                });
            }
        }
    }
});
</script>
<?= $this->endSection() ?>
