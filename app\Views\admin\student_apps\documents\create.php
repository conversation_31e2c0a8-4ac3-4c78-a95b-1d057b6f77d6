<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?>Create <?= $entity_name ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h2 class="text-title-md2 font-semibold text-black dark:text-white">
            Create <?= $entity_name ?>
        </h2>
        <nav>
            <ol class="flex items-center gap-2">
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                        <li class="text-body-color">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Form Card -->
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-6.5 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">
                Create New <?= $entity_name ?>
            </h3>
        </div>
        
        <form id="createForm" action="<?= base_url($route_prefix . '/store') ?>" method="POST" enctype="multipart/form-data">
            <?= csrf_field() ?>
            <div class="p-6.5">
                <div class="mb-4.5 flex flex-col gap-6 xl:flex-row">
                    <!-- Student Selection -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Student <span class="text-meta-1">*</span>
                        </label>
                        <select name="student_id" id="student_id" class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                            <option value="">Select Student</option>
                            <?php if (isset($students)): ?>
                                <?php foreach ($students as $student): ?>
                                    <option value="<?= $student['id'] ?>"><?= $student['firstname'] . ' ' . $student['lastname'] . ' (' . $student['admission_no'] . ')' ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <!-- Document Title -->
                    <div class="w-full xl:w-1/2">
                        <label class="mb-2.5 block text-black dark:text-white">
                            Document Title <span class="text-meta-1">*</span>
                        </label>
                        <input type="text" name="title" id="title" placeholder="Enter document title" 
                               class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                    </div>
                </div>



                <!-- Document File -->
                <div class="mb-4.5">
                    <label class="mb-2.5 block text-black dark:text-white">
                        Document File <span class="text-meta-1">*</span>
                    </label>
                    <div class="file-upload-area" id="file-upload-area">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <p class="text-lg font-medium text-gray-700 mb-2">Drop your file here or click to browse</p>
                        <p class="text-sm text-gray-500 mb-4">Allowed formats: PDF, DOC, DOCX, JPG, JPEG, PNG (Max: 5MB)</p>
                        <input type="file" name="doc_file" id="doc_file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                               class="hidden" required>
                        <button type="button" id="browse-btn" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition">
                            <i class="fas fa-folder-open mr-2"></i>Browse Files
                        </button>
                        <div class="file-info" id="file-info">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-file mr-2"></i>
                                    <span id="file-name"></span>
                                </div>
                                <div class="text-sm">
                                    <span id="file-size"></span>
                                    <button type="button" id="remove-file" class="ml-2 text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Action Buttons -->
                <div class="flex gap-4.5">
                    <button type="submit" class="flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90">
                        <i class="fas fa-save mr-2"></i>
                        Create Document
                    </button>
                    <a href="<?= base_url($route_prefix) ?>" class="flex w-full justify-center rounded border border-stroke p-3 font-medium text-black hover:shadow-1 dark:border-strokedark dark:text-white">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
.upload-progress-container {
    padding: 1rem;
}

.progress-bar-container {
    position: relative;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#progress-bar {
    position: relative;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.file-upload-area {
    transition: all 0.3s ease;
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background: #f9fafb;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.file-upload-area.dragover {
    border-color: #1d4ed8;
    background: #dbeafe;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.file-info {
    display: none;
    margin-top: 1rem;
    padding: 0.75rem;
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 0.375rem;
    color: #0c4a6e;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createForm');
    const fileInput = document.getElementById('doc_file');
    const fileUploadArea = document.getElementById('file-upload-area');
    const browseBtn = document.getElementById('browse-btn');
    const fileInfo = document.getElementById('file-info');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const removeFileBtn = document.getElementById('remove-file');

    // Browse button click
    browseBtn.addEventListener('click', function() {
        fileInput.click();
    });

    // File upload area click
    fileUploadArea.addEventListener('click', function(e) {
        if (e.target === fileUploadArea || e.target.closest('.upload-icon') || e.target.closest('p')) {
            fileInput.click();
        }
    });

    // Drag and drop functionality
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelection(files[0]);
        }
    });

    // File input change
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            handleFileSelection(file);
        }
    });

    // Remove file button
    removeFileBtn.addEventListener('click', function() {
        fileInput.value = '';
        fileInfo.style.display = 'none';
        fileUploadArea.style.display = 'block';
    });

    // Handle file selection
    function handleFileSelection(file) {
        // Validate file size
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            Swal.fire({
                icon: 'error',
                title: 'File Too Large',
                text: 'File size must be less than 5MB'
            });
            fileInput.value = '';
            return;
        }

        // Validate file type
        const allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
        const fileExtension = file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid File Type',
                text: 'Please select a valid file type: PDF, DOC, DOCX, JPG, JPEG, PNG'
            });
            fileInput.value = '';
            return;
        }

        // Show file info
        fileName.textContent = file.name;
        fileSize.textContent = (file.size / 1024 / 1024).toFixed(2) + ' MB';
        fileInfo.style.display = 'block';

        // Hide upload area
        const uploadElements = fileUploadArea.querySelectorAll('.upload-icon, p, button');
        uploadElements.forEach(el => el.style.display = 'none');
    }
    
    // Form submission with upload progress
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const fileInput = document.getElementById('doc_file');
        const file = fileInput.files[0];

        // Check if file is selected
        if (!file) {
            Swal.fire({
                icon: 'warning',
                title: 'No File Selected',
                text: 'Please select a document file to upload'
            });
            return;
        }

        // Show upload progress modal
        let uploadXhr; // Store xhr reference for cancellation

        Swal.fire({
            title: 'Uploading Document...',
            html: `
                <div class="upload-progress-container">
                    <div class="progress-bar-container" style="width: 100%; background-color: #e5e7eb; border-radius: 0.5rem; overflow: hidden; margin: 1rem 0;">
                        <div id="progress-bar" style="width: 0%; height: 1.5rem; background: linear-gradient(90deg, #3b82f6, #1d4ed8); transition: width 0.3s ease; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.875rem; font-weight: 500;"></div>
                    </div>
                    <div id="progress-text" style="text-align: center; color: #6b7280; font-size: 0.875rem;">Preparing upload...</div>
                    <div id="upload-details" style="text-align: center; color: #9ca3af; font-size: 0.75rem; margin-top: 0.5rem;">
                        File: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                    </div>
                </div>
            `,
            allowOutsideClick: false,
            showCancelButton: true,
            cancelButtonText: 'Cancel Upload',
            cancelButtonColor: '#ef4444',
            showConfirmButton: false,
            preConfirm: () => {
                // This won't be called since showConfirmButton is false
                return false;
            },
            didOpen: () => {
                // Create XMLHttpRequest for progress tracking
                const xhr = new XMLHttpRequest();
                uploadXhr = xhr; // Store reference for cancellation

                // Handle cancel button click
                const cancelBtn = Swal.getCancelButton();
                if (cancelBtn) {
                    cancelBtn.addEventListener('click', () => {
                        if (uploadXhr) {
                            uploadXhr.abort();
                            Swal.fire({
                                icon: 'info',
                                title: 'Upload Cancelled',
                                text: 'Document upload has been cancelled',
                                timer: 2000,
                                timerProgressBar: true
                            });
                        }
                    });
                }

                // Track upload progress
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = Math.round((e.loaded / e.total) * 100);
                        const progressBar = document.getElementById('progress-bar');
                        const progressText = document.getElementById('progress-text');

                        if (progressBar && progressText) {
                            progressBar.style.width = percentComplete + '%';
                            progressBar.textContent = percentComplete + '%';

                            if (percentComplete < 100) {
                                progressText.textContent = `Uploading... ${percentComplete}% (${(e.loaded / 1024 / 1024).toFixed(2)} MB of ${(e.total / 1024 / 1024).toFixed(2)} MB)`;
                            } else {
                                progressText.textContent = 'Processing document...';
                            }
                        }
                    }
                });

                // Handle completion
                xhr.addEventListener('load', function() {
                    try {
                        const data = JSON.parse(xhr.responseText);

                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success!',
                                text: data.message || 'Document uploaded successfully',
                                timer: 2000,
                                timerProgressBar: true
                            }).then(() => {
                                window.location.href = '<?= base_url($route_prefix) ?>';
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Upload Failed!',
                                text: data.message || 'Failed to upload document',
                                footer: data.errors ? Object.values(data.errors).flat().join('<br>') : ''
                            });
                        }
                    } catch (error) {
                        console.error('Parse error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'Invalid response from server'
                        });
                    }
                });

                // Handle errors
                xhr.addEventListener('error', function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Upload Error!',
                        text: 'Network error occurred during upload'
                    });
                });

                // Handle timeout
                xhr.addEventListener('timeout', function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Upload Timeout!',
                        text: 'Upload took too long. Please try again.'
                    });
                });

                // Handle abort (cancellation)
                xhr.addEventListener('abort', function() {
                    // Upload was cancelled, no need to show another alert
                    // as it's already handled in the cancel button click
                });

                // Configure and send request
                xhr.open('POST', form.action);
                xhr.timeout = 300000; // 5 minutes timeout
                xhr.send(formData);
            }
        });
    });
});
</script>
<?= $this->endSection() ?>
