<?php

namespace App\Controllers;

use App\Models\DashboardModel;

class Admin extends BaseController
{
    protected $dashboardModel;
    
    public function __construct()
    {
        $this->dashboardModel = new DashboardModel();
    }

    public function index()
    {
        return $this->dashboard();
    }

    public function dashboard()
    {
        $data = array_merge([
            'title' => 'Dashboard',
            'stats' => $this->dashboardModel->getDashboardStats(),
            'recent_activities' => $this->dashboardModel->getRecentActivities(),
            'charts_data' => $this->dashboardModel->getChartsData()
        ], $this->getCurrentUserData());

        return view('admin/dashboard', $data);
    }

    public function students()
    {
        $data = array_merge([
            'title' => 'Students Management',
            'students' => $this->dashboardModel->getStudents()
        ], $this->getCurrentUserData());

        return view('admin/students', $data);
    }

    public function staff()
    {
        $data = array_merge([
            'title' => 'Staff Management',
            'staff' => $this->dashboardModel->getStaff()
        ], $this->getCurrentUserData());

        return view('admin/staff', $data);
    }

    public function classes()
    {
        $data = array_merge([
            'title' => 'Classes Management',
            'classes' => $this->dashboardModel->getClasses()
        ], $this->getCurrentUserData());

        return view('admin/classes', $data);
    }

    public function fees()
    {
        $data = array_merge([
            'title' => 'Fees Management',
            'fees' => $this->dashboardModel->getFees()
        ], $this->getCurrentUserData());

        return view('admin/fees', $data);
    }

    public function expenses()
    {
        $data = array_merge([
            'title' => 'Expenses Management',
            'expenses' => $this->dashboardModel->getExpenses()
        ], $this->getCurrentUserData());

        return view('admin/expenses', $data);
    }

    public function reports()
    {
        $data = array_merge([
            'title' => 'Reports',
            'reports_data' => $this->dashboardModel->getReportsData()
        ], $this->getCurrentUserData());

        return view('admin/reports', $data);
    }

    public function settings()
    {
        $data = array_merge([
            'title' => 'Settings',
            'settings' => $this->dashboardModel->getSettings()
        ], $this->getCurrentUserData());

        return view('admin/settings', $data);
    }
}
