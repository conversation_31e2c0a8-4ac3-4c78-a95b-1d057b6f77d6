<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddUserProfileFields extends Migration
{
    public function up()
    {
        // Check if columns already exist before adding them
        $db = \Config\Database::connect();
        $fields = $db->getFieldData('users');
        $existingFields = array_column($fields, 'name');

        $fieldsToAdd = [];

        // Add first_name if it doesn't exist
        if (!in_array('first_name', $existingFields)) {
            $fieldsToAdd['first_name'] = [
                'type'       => 'VARCHAR',
                'constraint' => 100,
                'null'       => true,
                'after'      => 'username'
            ];
        }

        // Add last_name if it doesn't exist
        if (!in_array('last_name', $existingFields)) {
            $fieldsToAdd['last_name'] = [
                'type'       => 'VARCHAR',
                'constraint' => 100,
                'null'       => true,
                'after'      => 'first_name'
            ];
        }

        // Add email if it doesn't exist
        if (!in_array('email', $existingFields)) {
            $fieldsToAdd['email'] = [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
                'after'      => 'last_name'
            ];
        }

        // Add phone if it doesn't exist
        if (!in_array('phone', $existingFields)) {
            $fieldsToAdd['phone'] = [
                'type'       => 'VARCHAR',
                'constraint' => 20,
                'null'       => true,
                'after'      => 'email'
            ];
        }

        // Add avatar if it doesn't exist
        if (!in_array('avatar', $existingFields)) {
            $fieldsToAdd['avatar'] = [
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => true,
                'after'      => 'phone'
            ];
        }

        // Add google_id if it doesn't exist
        if (!in_array('google_id', $existingFields)) {
            $fieldsToAdd['google_id'] = [
                'type'       => 'VARCHAR',
                'constraint' => 100,
                'null'       => true,
                'after'      => 'avatar'
            ];
        }

        // Add email_verified_at if it doesn't exist
        if (!in_array('email_verified_at', $existingFields)) {
            $fieldsToAdd['email_verified_at'] = [
                'type' => 'DATETIME',
                'null' => true,
                'after' => 'google_id'
            ];
        }

        // Add profile_completed if it doesn't exist
        if (!in_array('profile_completed', $existingFields)) {
            $fieldsToAdd['profile_completed'] = [
                'type'    => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'after'   => 'email_verified_at'
            ];
        }

        // Add the fields if any need to be added
        if (!empty($fieldsToAdd)) {
            $this->forge->addColumn('users', $fieldsToAdd);
        }

        // Add indexes for better performance
        $indexes = $db->getIndexData('users');
        $existingIndexes = array_column($indexes, 'name');

        if (!in_array('idx_users_email', $existingIndexes)) {
            $this->forge->addKey('email', false, false, 'idx_users_email');
        }

        if (!in_array('idx_users_google_id', $existingIndexes)) {
            $this->forge->addKey('google_id', false, false, 'idx_users_google_id');
        }

        if (!in_array('idx_users_first_last_name', $existingIndexes)) {
            $this->forge->addKey(['first_name', 'last_name'], false, false, 'idx_users_first_last_name');
        }
    }

    public function down()
    {
        // Check if columns exist before dropping them
        $db = \Config\Database::connect();
        $fields = $db->getFieldData('users');
        $existingFields = array_column($fields, 'name');

        $fieldsToRemove = [];

        // List of fields to remove
        $profileFields = [
            'first_name',
            'last_name', 
            'email',
            'phone',
            'avatar',
            'google_id',
            'email_verified_at',
            'profile_completed'
        ];

        foreach ($profileFields as $field) {
            if (in_array($field, $existingFields)) {
                $fieldsToRemove[] = $field;
            }
        }

        // Remove the fields if they exist
        if (!empty($fieldsToRemove)) {
            $this->forge->dropColumn('users', $fieldsToRemove);
        }

        // Drop indexes
        $indexes = $db->getIndexData('users');
        $existingIndexes = array_column($indexes, 'name');

        if (in_array('idx_users_email', $existingIndexes)) {
            $this->forge->dropKey('users', 'idx_users_email');
        }

        if (in_array('idx_users_google_id', $existingIndexes)) {
            $this->forge->dropKey('users', 'idx_users_google_id');
        }

        if (in_array('idx_users_first_last_name', $existingIndexes)) {
            $this->forge->dropKey('users', 'idx_users_first_last_name');
        }
    }
}
