<?php

namespace App\Controllers;

use CodeIgniter\Shield\Models\UserModel;
use CodeIgniter\HTTP\RedirectResponse;

class ProfileController extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    /**
     * Display user profile
     */
    public function index()
    {
        // Check if user is logged in
        if (!auth()->loggedIn()) {
            return redirect()->to('/login')->with('error', 'Please login to access your profile.');
        }

        $user = auth()->user();
        
        // Get user groups/roles
        $groups = $user->getGroups();
        $primaryRole = !empty($groups) ? $groups[0] : 'user';

        $data = array_merge([
            'title' => 'My Profile',
            'user' => $user,
            'primary_role' => $primaryRole,
            'groups' => $groups,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'My Profile', 'url' => '']
            ]
        ], $this->getCurrentUserData());

        return view('admin/profile/index', $data);
    }

    /**
     * Show edit profile form
     */
    public function edit()
    {
        // Check if user is logged in
        if (!auth()->loggedIn()) {
            return redirect()->to('/login')->with('error', 'Please login to access your profile.');
        }

        $user = auth()->user();

        $data = array_merge([
            'title' => 'Edit Profile',
            'user' => $user,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'My Profile', 'url' => base_url('admin/profile')],
                ['name' => 'Edit', 'url' => '']
            ]
        ], $this->getCurrentUserData());

        return view('admin/profile/edit', $data);
    }

    /**
     * Update user profile
     */
    public function update(): RedirectResponse
    {
        // Check if user is logged in
        if (!auth()->loggedIn()) {
            return redirect()->to('/login')->with('error', 'Please login to access your profile.');
        }

        $user = auth()->user();

        // Validation rules
        $rules = [
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name'  => 'required|min_length[2]|max_length[50]',
            'email'      => 'required|valid_email|is_unique[users.email,id,' . $user->id . ']',
            'phone'      => 'permit_empty|min_length[10]|max_length[15]',
        ];

        if (!$this->validateData($this->request->getPost(), $rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        try {
            // Update user data
            $updateData = [
                'first_name' => $this->request->getPost('first_name'),
                'last_name'  => $this->request->getPost('last_name'),
                'phone'      => $this->request->getPost('phone'),
            ];

            // Update email if changed
            $newEmail = $this->request->getPost('email');
            if ($newEmail !== $user->email) {
                $updateData['email'] = $newEmail;
            }

            $this->userModel->update($user->id, $updateData);

            return redirect()->to('/admin/profile')->with('success', 'Profile updated successfully!');

        } catch (\Exception $e) {
            log_message('error', 'Profile update error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to update profile. Please try again.');
        }
    }

    /**
     * Show change password form
     */
    public function changePassword()
    {
        // Check if user is logged in
        if (!auth()->loggedIn()) {
            return redirect()->to('/login')->with('error', 'Please login to access your profile.');
        }

        $data = array_merge([
            'title' => 'Change Password',
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'My Profile', 'url' => base_url('admin/profile')],
                ['name' => 'Change Password', 'url' => '']
            ]
        ], $this->getCurrentUserData());

        return view('admin/profile/change_password', $data);
    }

    /**
     * Update user password
     */
    public function updatePassword(): RedirectResponse
    {
        // Check if user is logged in
        if (!auth()->loggedIn()) {
            return redirect()->to('/login')->with('error', 'Please login to access your profile.');
        }

        $user = auth()->user();

        // Validation rules
        $rules = [
            'current_password'     => 'required',
            'new_password'         => 'required|min_length[8]|strong_password',
            'confirm_new_password' => 'required|matches[new_password]',
        ];

        if (!$this->validateData($this->request->getPost(), $rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        try {
            $currentPassword = $this->request->getPost('current_password');
            $newPassword = $this->request->getPost('new_password');

            // Verify current password
            if (!auth()->check(['email' => $user->email, 'password' => $currentPassword])) {
                return redirect()->back()->withInput()->with('error', 'Current password is incorrect.');
            }

            // Update password
            $user->password = $newPassword;
            $this->userModel->save($user);

            return redirect()->to('/admin/profile')->with('success', 'Password changed successfully!');

        } catch (\Exception $e) {
            log_message('error', 'Password change error: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'Failed to change password. Please try again.');
        }
    }

    /**
     * Get user avatar URL
     */
    public function getAvatarUrl($user = null)
    {
        if (!$user) {
            $user = auth()->user();
        }

        if (!$user) {
            return 'https://ui-avatars.com/api/?name=User&background=3b82f6&color=fff&size=48';
        }

        // Check if user has uploaded avatar
        if (!empty($user->avatar) && file_exists(WRITEPATH . 'uploads/avatars/' . $user->avatar)) {
            return base_url('writable/uploads/avatars/' . $user->avatar);
        }

        // Generate avatar from name
        $name = trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? ''));
        if (empty($name)) {
            $name = $user->username ?? $user->email ?? 'User';
        }

        return 'https://ui-avatars.com/api/?name=' . urlencode($name) . '&background=3b82f6&color=fff&size=48';
    }

    /**
     * Get user display name
     */
    public function getDisplayName($user = null)
    {
        if (!$user) {
            $user = auth()->user();
        }

        if (!$user) {
            return 'Guest User';
        }

        $name = trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? ''));
        if (!empty($name)) {
            return $name;
        }

        return $user->username ?? $user->email ?? 'User';
    }

    /**
     * Get user role display name
     */
    public function getRoleDisplayName($user = null)
    {
        if (!$user) {
            $user = auth()->user();
        }

        if (!$user) {
            return 'Guest';
        }

        $groups = $user->getGroups();
        if (empty($groups)) {
            return 'User';
        }

        $roleMap = [
            'superadmin' => 'Super Administrator',
            'admin'      => 'Administrator',
            'teacher'    => 'Teacher',
            'staff'      => 'Staff',
            'parent'     => 'Parent',
            'student'    => 'Student',
        ];

        $primaryRole = $groups[0];
        return $roleMap[$primaryRole] ?? ucfirst($primaryRole);
    }
}
