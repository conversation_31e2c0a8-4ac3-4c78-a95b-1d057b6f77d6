<?php

namespace App\Controllers;

use App\Models\ExpensesModel;
use App\Models\ExpenseHeadModel;

class ExpensesController extends BaseCrudController
{
    protected $expenseHeadModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new ExpensesModel();
        $this->expenseHeadModel = new ExpenseHeadModel();
        
        $this->viewPath = 'admin/expenses';
        $this->routePrefix = 'admin/expenses';
        $this->entityName = 'Expense';
        $this->entityNamePlural = 'Expenses';
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'expense_heads' => $this->getExpenseHeads(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => $record ? 'Edit' : 'Add New', 'url' => '']
            ]
        ];
    }

    /**
     * Get expense heads for dropdown
     */
    private function getExpenseHeads()
    {
        try {
            return $this->expenseHeadModel->getForDropdown();
        } catch (\Exception $e) {
            return [
                1 => 'Office Supplies',
                2 => 'Utilities',
                3 => 'Maintenance',
                4 => 'Transportation'
            ];
        }
    }

    /**
     * Process form data before saving
     */
    protected function processFormData($data, $id = null)
    {
        // Set default values
        $data['is_active'] = $data['is_active'] ?? 'yes';
        $data['is_deleted'] = 'no';
        $data['date'] = $data['date'] ?? date('Y-m-d');

        return $data;
    }

    /**
     * Process file uploads
     */
    protected function processFileUploads($data, $id = null)
    {
        $uploadPath = WRITEPATH . 'uploads/expenses/';
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Handle document upload
        $documentFile = $this->request->getFile('documents');
        if ($documentFile && $documentFile->isValid() && !$documentFile->hasMoved()) {
            $newName = $documentFile->getRandomName();
            $documentFile->move($uploadPath, $newName);
            $data['documents'] = $newName;
        }

        return $data;
    }

    /**
     * Get expenses with filters
     */
    public function getFiltered()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'exp_head_id' => $this->request->getPost('exp_head_id'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'month' => $this->request->getPost('month'),
            'year' => $this->request->getPost('year')
        ];

        // Remove empty filters
        $filters = array_filter($filters);

        $expenses = $this->model->getExpensesWithCategory($filters);

        return $this->response->setJSON([
            'success' => true,
            'data' => $expenses
        ]);
    }

    /**
     * Get expense statistics
     */
    public function statistics()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get monthly expenses chart data
     */
    public function monthlyChart()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $year = $this->request->getPost('year') ?: date('Y');
        $monthlyData = $this->model->getMonthlyExpenses($year);

        return $this->response->setJSON([
            'success' => true,
            'data' => $monthlyData
        ]);
    }

    /**
     * Get expenses by category chart data
     */
    public function categoryChart()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $year = $this->request->getPost('year') ?: date('Y');
        $categoryData = $this->model->getExpensesByCategory($year);

        return $this->response->setJSON([
            'success' => true,
            'data' => $categoryData
        ]);
    }

    /**
     * Soft delete expense
     */
    public function softDelete($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $result = $this->model->softDelete($id);

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Expense moved to trash successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete expense'
            ]);
        }
    }

    /**
     * Restore soft deleted expense
     */
    public function restore($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $result = $this->model->restore($id);

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Expense restored successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to restore expense'
            ]);
        }
    }

    /**
     * View deleted expenses
     */
    public function trash()
    {
        $data = [
            'title' => 'Deleted Expenses',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'deleted_expenses' => $this->model->getDeleted(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Trash', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/trash', $data);
    }

    /**
     * Expense reports page
     */
    public function reports()
    {
        $data = array_merge([
            'title' => 'Expense Reports',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'expense_heads' => $this->getExpenseHeads(),
            'years' => $this->getYearOptions(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Reports', 'url' => '']
            ]
        ], $this->getCurrentUserData());

        return view($this->viewPath . '/reports', $data);
    }

    /**
     * Get year options for reports
     */
    private function getYearOptions()
    {
        $currentYear = date('Y');
        $years = [];
        
        for ($i = $currentYear; $i >= ($currentYear - 5); $i--) {
            $years[$i] = $i;
        }
        
        return $years;
    }

    /**
     * Generate expense report
     */
    public function generateReport()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $reportType = $this->request->getPost('report_type');
        $filters = [
            'exp_head_id' => $this->request->getPost('exp_head_id'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'month' => $this->request->getPost('month'),
            'year' => $this->request->getPost('year')
        ];

        // Remove empty filters
        $filters = array_filter($filters);

        switch ($reportType) {
            case 'monthly':
                $data = $this->model->getMonthlyExpenses($filters['year'] ?? date('Y'));
                break;
            case 'category':
                $data = $this->model->getExpensesByCategory($filters['year'] ?? date('Y'));
                break;
            case 'detailed':
                $data = $this->model->getExpensesWithCategory($filters);
                break;
            default:
                $data = [];
        }

        return $this->response->setJSON([
            'success' => true,
            'data' => $data,
            'report_type' => $reportType
        ]);
    }
}
