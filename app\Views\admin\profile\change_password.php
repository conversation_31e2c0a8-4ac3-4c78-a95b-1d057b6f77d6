<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-bold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="text-primary"><?= esc($breadcrumb['name']) ?></li>
                <?php else: ?>
                    <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= esc($breadcrumb['name']) ?></a></li>
                    <li class="text-gray-500">/</li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
</div>

<!-- Change Password Form -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Change Password
        </h3>
    </div>
    
    <div class="p-7">
        <!-- Security Notice -->
        <div class="mb-6 rounded-md bg-warning bg-opacity-10 border border-warning border-opacity-20 p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-shield-alt text-warning text-lg"></i>
                </div>
                <div class="ml-3">
                    <h4 class="text-sm font-medium text-warning">Security Notice</h4>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        Please ensure your new password is strong and unique. It should contain at least 8 characters with a mix of uppercase, lowercase, numbers, and special characters.
                    </p>
                </div>
            </div>
        </div>

        <?= form_open('admin/profile/update-password', ['class' => 'space-y-6']) ?>
        
        <div class="max-w-md mx-auto">
            <!-- Current Password -->
            <div class="mb-6">
                <label class="mb-2.5 block text-black dark:text-white">
                    Current Password <span class="text-meta-1">*</span>
                </label>
                <div class="relative">
                    <input
                        type="password"
                        name="current_password"
                        placeholder="Enter your current password"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 pr-12 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                        required
                    />
                    <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3 toggle-password">
                        <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                    </button>
                </div>
                <?php if (isset($errors['current_password'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['current_password']) ?></p>
                <?php endif; ?>
            </div>

            <!-- New Password -->
            <div class="mb-6">
                <label class="mb-2.5 block text-black dark:text-white">
                    New Password <span class="text-meta-1">*</span>
                </label>
                <div class="relative">
                    <input
                        type="password"
                        name="new_password"
                        placeholder="Enter your new password"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 pr-12 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                        required
                    />
                    <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3 toggle-password">
                        <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                    </button>
                </div>
                <?php if (isset($errors['new_password'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['new_password']) ?></p>
                <?php endif; ?>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Password must be at least 8 characters long and contain uppercase, lowercase, numbers, and special characters.
                </p>
            </div>

            <!-- Confirm New Password -->
            <div class="mb-6">
                <label class="mb-2.5 block text-black dark:text-white">
                    Confirm New Password <span class="text-meta-1">*</span>
                </label>
                <div class="relative">
                    <input
                        type="password"
                        name="confirm_new_password"
                        placeholder="Confirm your new password"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 pr-12 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                        required
                    />
                    <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3 toggle-password">
                        <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                    </button>
                </div>
                <?php if (isset($errors['confirm_new_password'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['confirm_new_password']) ?></p>
                <?php endif; ?>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col gap-4 sm:flex-row sm:justify-center">
                <a href="<?= base_url('admin/profile') ?>" 
                   class="inline-flex items-center justify-center rounded-md border border-stroke py-3 px-6 text-center font-medium text-black hover:bg-opacity-90 dark:border-strokedark dark:text-white lg:px-8 xl:px-10">
                    <i class="fas fa-times mr-2"></i>
                    Cancel
                </a>
                <button
                    type="submit"
                    class="inline-flex items-center justify-center rounded-md bg-primary py-3 px-6 text-center font-medium text-white hover:bg-opacity-90 lg:px-8 xl:px-10"
                >
                    <i class="fas fa-key mr-2"></i>
                    Change Password
                </button>
            </div>
        </div>
        
        <?= form_close() ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const toggleButtons = document.querySelectorAll('.toggle-password');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
    
    // Password strength indicator
    const newPasswordInput = document.querySelector('input[name="new_password"]');
    if (newPasswordInput) {
        newPasswordInput.addEventListener('input', function() {
            // You can add password strength indicator here
        });
    }
});
</script>

<?= $this->endSection() ?>
