<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use CodeIgniter\Shield\Models\UserModel;

class ShowUserData extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'show:user-data';
    protected $description = 'Display user data from the database';

    public function run(array $params)
    {
        $userModel = new UserModel();
        
        // Get user ID from parameter or show all users
        $userId = $params[0] ?? null;
        
        if ($userId) {
            $this->showSingleUser($userModel, $userId);
        } else {
            $this->showAllUsers($userModel);
        }
    }
    
    protected function showSingleUser($userModel, $userId)
    {
        CLI::write('🔍 User Data for ID: ' . $userId, 'yellow');
        CLI::newLine();
        
        $user = $userModel->find($userId);
        
        if (!$user) {
            CLI::write('❌ User not found with ID: ' . $userId, 'red');
            return;
        }
        
        $this->displayUserInfo($user);
    }
    
    protected function showAllUsers($userModel)
    {
        CLI::write('👥 All Users Data', 'yellow');
        CLI::newLine();
        
        $users = $userModel->findAll();
        
        if (empty($users)) {
            CLI::write('❌ No users found', 'red');
            return;
        }
        
        foreach ($users as $user) {
            $this->displayUserInfo($user);
            CLI::newLine();
        }
    }
    
    protected function displayUserInfo($user)
    {
        // Convert array to object if needed
        if (is_array($user)) {
            $user = (object) $user;
        }
        
        CLI::write('📋 User ID: ' . ($user->id ?? 'N/A'), 'cyan');
        CLI::write('   Username: ' . ($user->username ?? 'N/A'), 'white');
        CLI::write('   First Name: ' . ($user->first_name ?? 'N/A'), 'white');
        CLI::write('   Last Name: ' . ($user->last_name ?? 'N/A'), 'white');
        CLI::write('   Email: ' . ($user->email ?? 'N/A'), 'white');
        CLI::write('   Phone: ' . ($user->phone ?? 'N/A'), 'white');
        CLI::write('   Google ID: ' . ($user->google_id ?? 'N/A'), 'white');
        CLI::write('   Avatar: ' . ($user->avatar ?? 'N/A'), 'white');
        CLI::write('   Email Verified: ' . ($user->email_verified_at ?? 'No'), 'white');
        CLI::write('   Profile Completed: ' . ($user->profile_completed ? 'Yes' : 'No'), 'white');
        CLI::write('   Active: ' . ($user->active ? 'Yes' : 'No'), 'white');
        CLI::write('   Last Active: ' . ($user->last_active ?? 'Never'), 'white');
        CLI::write('   Created: ' . ($user->created_at ?? 'N/A'), 'white');
        CLI::write('   Updated: ' . ($user->updated_at ?? 'N/A'), 'white');
        
        // Show user groups if available
        if (is_object($user) && method_exists($user, 'getGroups')) {
            try {
                $groups = $user->getGroups();
                CLI::write('   Groups: ' . (empty($groups) ? 'None' : implode(', ', $groups)), 'white');
            } catch (\Exception $e) {
                CLI::write('   Groups: Error loading groups', 'red');
            }
        }
    }
}
