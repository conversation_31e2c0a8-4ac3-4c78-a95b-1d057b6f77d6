<?php

namespace App\Controllers;

use CodeIgniter\HTTP\ResponseInterface;

class BaseCrudController extends BaseController
{
    protected $model;
    protected $viewPath;
    protected $routePrefix;
    protected $entityName;
    protected $entityNamePlural;

    public function __construct()
    {
        helper(['form', 'url']);
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = array_merge([
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ], $this->getCurrentUserData());

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get data for DataTables AJAX
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $request = $this->request->getPost();
        $data = $this->model->getDataTableData($request);

        return $this->response->setJSON($data);
    }

    /**
     * Show create form
     */
    public function create()
    {
        $data = array_merge([
            'title' => 'Add New ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Add New', 'url' => '']
            ]
        ], $this->getCurrentUserData());

        // Add any additional data needed for the form
        $data = array_merge($data, $this->getFormData());

        return view($this->viewPath . '/create', $data);
    }

    /**
     * Store new record
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        
        // Process any file uploads
        $data = $this->processFileUploads($data);
        
        // Additional data processing
        $data = $this->processFormData($data);

        $result = $this->model->createRecord($data);

        if ($result['success']) {
            return $this->response->setJSON([
                'success' => true,
                'message' => $this->entityName . ' created successfully',
                'data' => $result['data']
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create ' . strtolower($this->entityName),
                'errors' => $result['errors']
            ]);
        }
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $record = $this->model->find($id);

        if (!$record) {
            return redirect()->to($this->routePrefix)->with('error', $this->entityName . ' not found');
        }

        $data = array_merge([
            'title' => 'Edit ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'record' => $record,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Edit', 'url' => '']
            ]
        ], $this->getCurrentUserData());

        // Add any additional data needed for the form
        $data = array_merge($data, $this->getFormData($record));

        return view($this->viewPath . '/edit', $data);
    }

    /**
     * Update record
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        
        // Process any file uploads
        $data = $this->processFileUploads($data, $id);
        
        // Additional data processing
        $data = $this->processFormData($data, $id);

        $result = $this->model->updateRecord($id, $data);

        if ($result['success']) {
            return $this->response->setJSON([
                'success' => true,
                'message' => $this->entityName . ' updated successfully',
                'data' => $result['data']
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update ' . strtolower($this->entityName),
                'errors' => $result['errors']
            ]);
        }
    }

    /**
     * Delete record
     */
    public function delete($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $result = $this->model->deleteRecord($id);

        if ($result['success']) {
            return $this->response->setJSON([
                'success' => true,
                'message' => $this->entityName . ' deleted successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete ' . strtolower($this->entityName),
                'errors' => $result['errors']
            ]);
        }
    }

    /**
     * Toggle status
     */
    public function toggleStatus($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $result = $this->model->toggleStatus($id);

        if ($result['success']) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Status updated successfully',
                'data' => $result['data']
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update status',
                'errors' => $result['errors']
            ]);
        }
    }

    /**
     * Bulk delete
     */
    public function bulkDelete()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $ids = $this->request->getPost('ids');
        
        if (empty($ids) || !is_array($ids)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No records selected'
            ]);
        }

        $successCount = 0;
        $errors = [];

        foreach ($ids as $id) {
            $result = $this->model->deleteRecord($id);
            if ($result['success']) {
                $successCount++;
            } else {
                $errors[] = "Failed to delete record ID: $id";
            }
        }

        if ($successCount > 0) {
            $message = "$successCount " . strtolower($this->entityNamePlural) . " deleted successfully";
            if (!empty($errors)) {
                $message .= ". Some records could not be deleted.";
            }
            
            return $this->response->setJSON([
                'success' => true,
                'message' => $message,
                'errors' => $errors
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete selected records',
                'errors' => $errors
            ]);
        }
    }

    /**
     * Export data
     */
    public function export($format = 'csv')
    {
        $data = $this->model->findAll();
        
        switch ($format) {
            case 'csv':
                return $this->exportCSV($data);
            case 'excel':
                return $this->exportExcel($data);
            case 'pdf':
                return $this->exportPDF($data);
            default:
                return redirect()->back()->with('error', 'Invalid export format');
        }
    }

    /**
     * Get form data (override in child controllers)
     */
    protected function getFormData($record = null)
    {
        return [];
    }

    /**
     * Process form data before saving (override in child controllers)
     */
    protected function processFormData($data, $id = null)
    {
        return $data;
    }

    /**
     * Process file uploads (override in child controllers)
     */
    protected function processFileUploads($data, $id = null)
    {
        return $data;
    }

    /**
     * Export data as CSV
     */
    protected function exportCSV($data)
    {
        $filename = strtolower($this->entityNamePlural) . '_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        if (!empty($data)) {
            // Write headers
            fputcsv($output, array_keys($data[0]));
            
            // Write data
            foreach ($data as $row) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
        exit;
    }

    /**
     * Export data as Excel (basic implementation)
     */
    protected function exportExcel($data)
    {
        // This would require a library like PhpSpreadsheet
        // For now, we'll export as CSV with Excel-friendly format
        return $this->exportCSV($data);
    }

    /**
     * Export data as PDF (basic implementation)
     */
    protected function exportPDF($data)
    {
        // This would require a library like TCPDF or DOMPDF
        // For now, we'll redirect back with a message
        return redirect()->back()->with('info', 'PDF export feature coming soon');
    }

    /**
     * Show record details
     */
    public function show($id)
    {
        $record = $this->model->getById($id, $this->getRelationships());

        if (!$record) {
            return redirect()->to($this->routePrefix)->with('error', $this->entityName . ' not found');
        }

        $data = array_merge([
            'title' => 'View ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'record' => $record,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'View', 'url' => '']
            ]
        ], $this->getCurrentUserData());

        return view($this->viewPath . '/show', $data);
    }

    /**
     * Get relationships to load (override in child controllers)
     */
    protected function getRelationships()
    {
        return [];
    }
}
