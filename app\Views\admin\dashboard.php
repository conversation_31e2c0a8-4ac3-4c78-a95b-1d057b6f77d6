<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        Dashboard
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <li>
                <a class="font-medium" href="<?= base_url('admin') ?>">Dashboard /</a>
            </li>
            <li class="font-medium text-primary">Overview</li>
        </ol>
    </nav>
</div>

<!-- Dashboard Stats -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-8">
    <!-- Total Students -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-user-graduate text-primary text-xl"></i>
        </div>

        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white">
                    <?= number_format($stats['total_students'] ?? 0) ?>
                </h4>
                <span class="text-sm font-medium">Total Students</span>
            </div>

            <span class="flex items-center gap-1 text-sm font-medium text-meta-3">
                <i class="fas fa-arrow-up text-xs"></i>
                4.35%
            </span>
        </div>
    </div>

    <!-- Total Staff -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-chalkboard-teacher text-primary text-xl"></i>
        </div>

        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white">
                    <?= number_format($stats['total_staff'] ?? 0) ?>
                </h4>
                <span class="text-sm font-medium">Total Staff</span>
            </div>

            <span class="flex items-center gap-1 text-sm font-medium text-meta-3">
                <i class="fas fa-arrow-up text-xs"></i>
                2.59%
            </span>
        </div>
    </div>

    <!-- Total Classes -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-school text-primary text-xl"></i>
        </div>

        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white">
                    <?= number_format($stats['total_classes'] ?? 0) ?>
                </h4>
                <span class="text-sm font-medium">Total Classes</span>
            </div>

            <span class="flex items-center gap-1 text-sm font-medium text-meta-5">
                <i class="fas fa-arrow-down text-xs"></i>
                0.95%
            </span>
        </div>
    </div>

    <!-- Monthly Expenses -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-money-bill-wave text-primary text-xl"></i>
        </div>

        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white">
                    $<?= number_format($stats['monthly_expenses'] ?? 0, 2) ?>
                </h4>
                <span class="text-sm font-medium">Monthly Expenses</span>
            </div>

            <span class="flex items-center gap-1 text-sm font-medium text-meta-5">
                <i class="fas fa-arrow-down text-xs"></i>
                1.10%
            </span>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-7.5 2xl:gap-7.5">
    <!-- Enrollment Chart -->
    <div class="col-span-12 xl:col-span-8">
        <div class="rounded-sm border border-stroke bg-white px-5 pt-7.5 pb-5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5">
            <div class="flex flex-wrap items-start justify-between gap-3 sm:flex-nowrap">
                <div class="flex w-full flex-wrap gap-3 sm:gap-5">
                    <div class="flex min-w-47.5">
                        <span class="mt-1 mr-2 flex h-4 w-full max-w-4 items-center justify-center rounded-full border border-primary">
                            <span class="block h-2.5 w-full max-w-2.5 rounded-full bg-primary"></span>
                        </span>
                        <div class="w-full">
                            <p class="font-semibold text-primary">Total Enrollments</p>
                            <p class="text-sm font-medium">12.04.2022 - 12.05.2022</p>
                        </div>
                    </div>
                </div>
                <div class="flex w-full max-w-45 justify-end">
                    <div class="inline-flex items-center rounded-md bg-whiter p-1.5 dark:bg-meta-4">
                        <button class="rounded bg-white py-1 px-3 text-xs font-medium text-black shadow-card hover:bg-white hover:shadow-card dark:bg-boxdark dark:text-white dark:hover:bg-boxdark">
                            Day
                        </button>
                        <button class="rounded py-1 px-3 text-xs font-medium text-black hover:bg-white hover:shadow-card dark:text-white dark:hover:bg-boxdark">
                            Week
                        </button>
                        <button class="rounded py-1 px-3 text-xs font-medium text-black hover:bg-white hover:shadow-card dark:text-white dark:hover:bg-boxdark">
                            Month
                        </button>
                    </div>
                </div>
            </div>

            <div>
                <div id="chartOne" class="-ml-5 h-[355px] w-[105%]">
                    <canvas id="enrollmentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Expenses Chart -->
    <div class="col-span-12 xl:col-span-4">
        <div class="rounded-sm border border-stroke bg-white px-5 pt-7.5 pb-5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5">
            <div class="mb-3 justify-between gap-4 sm:flex">
                <div>
                    <h5 class="text-xl font-semibold text-black dark:text-white">
                        Expenses Analytics
                    </h5>
                </div>
                <div>
                    <div class="relative z-20 inline-block">
                        <select class="relative z-20 inline-flex appearance-none bg-transparent py-1 pl-3 pr-8 text-sm font-medium outline-none">
                            <option value="">Monthly</option>
                            <option value="">Yearly</option>
                        </select>
                        <span class="absolute top-1/2 right-3 z-10 -translate-y-1/2">
                            <i class="fas fa-chevron-down text-sm"></i>
                        </span>
                    </div>
                </div>
            </div>

            <div class="mb-2">
                <div id="chartThree" class="mx-auto flex justify-center">
                    <canvas id="expensesChart" width="200" height="200"></canvas>
                </div>
            </div>

            <div class="-mx-8 flex flex-wrap items-center justify-center gap-y-3">
                <div class="w-full px-8 sm:w-1/2">
                    <div class="flex w-full items-center">
                        <span class="mr-2 block h-3 w-full max-w-3 rounded-full bg-primary"></span>
                        <p class="flex w-full justify-between text-sm font-medium text-black dark:text-white">
                            <span> Office Supplies </span>
                            <span> 65% </span>
                        </p>
                    </div>
                </div>
                <div class="w-full px-8 sm:w-1/2">
                    <div class="flex w-full items-center">
                        <span class="mr-2 block h-3 w-full max-w-3 rounded-full bg-[#6577F3]"></span>
                        <p class="flex w-full justify-between text-sm font-medium text-black dark:text-white">
                            <span> Utilities </span>
                            <span> 34% </span>
                        </p>
                    </div>
                </div>
                <div class="w-full px-8 sm:w-1/2">
                    <div class="flex w-full items-center">
                        <span class="mr-2 block h-3 w-full max-w-3 rounded-full bg-[#8FD0EF]"></span>
                        <p class="flex w-full justify-between text-sm font-medium text-black dark:text-white">
                            <span> Maintenance </span>
                            <span> 45% </span>
                        </p>
                    </div>
                </div>
                <div class="w-full px-8 sm:w-1/2">
                    <div class="flex w-full items-center">
                        <span class="mr-2 block h-3 w-full max-w-3 rounded-full bg-[#0FADCF]"></span>
                        <p class="flex w-full justify-between text-sm font-medium text-black dark:text-white">
                            <span> Other </span>
                            <span> 12% </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="col-span-12 mt-4 md:mt-6 2xl:mt-7.5">
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">Recent Activities</h3>
        </div>
        <div class="p-7">
            <?php if (!empty($recent_activities)): ?>
                <div class="flex flex-col gap-5">
                    <?php foreach ($recent_activities as $index => $activity): ?>
                        <div class="flex items-center gap-3">
                            <div class="flex h-9 w-9 items-center justify-center rounded-full border-[0.5px] border-stroke bg-gray dark:border-strokedark dark:bg-meta-4">
                                <i class="<?= $activity['icon'] ?> <?= $activity['color'] ?> text-sm"></i>
                            </div>
                            <div class="flex flex-1 items-center justify-between">
                                <div>
                                    <h5 class="font-medium text-black dark:text-white">
                                        <?= esc($activity['description']) ?>
                                    </h5>
                                    <p class="text-sm text-body">
                                        <?= date('M j, Y \a\t g:i A', strtotime($activity['date'])) ?>
                                    </p>
                                </div>
                                <div class="flex h-6 w-6 items-center justify-center rounded-full bg-primary">
                                    <span class="text-sm font-medium text-white"><?= $index + 1 ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray">
                        <i class="fas fa-inbox text-gray-400 text-xl"></i>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No activities</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first activity.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-7.5 2xl:gap-7.5">
    <div class="col-span-12">
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">Quick Actions</h3>
            </div>
            <div class="p-7">
                <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 xl:grid-cols-4">
                    <a href="<?= base_url('admin/students') ?>" class="rounded-sm border border-stroke bg-white p-7.5 shadow-default transition-all duration-300 hover:shadow-lg dark:border-strokedark dark:bg-boxdark">
                        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                            <i class="fas fa-user-plus text-primary text-xl"></i>
                        </div>
                        <div class="mt-4">
                            <h4 class="text-title-md font-bold text-black dark:text-white">
                                Add Student
                            </h4>
                            <p class="mt-1 text-sm">Register a new student in the system</p>
                        </div>
                    </a>

                    <a href="<?= base_url('admin/staff') ?>" class="rounded-sm border border-stroke bg-white p-7.5 shadow-default transition-all duration-300 hover:shadow-lg dark:border-strokedark dark:bg-boxdark">
                        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                            <i class="fas fa-user-tie text-primary text-xl"></i>
                        </div>
                        <div class="mt-4">
                            <h4 class="text-title-md font-bold text-black dark:text-white">
                                Add Staff
                            </h4>
                            <p class="mt-1 text-sm">Add new staff member to the system</p>
                        </div>
                    </a>

                    <a href="<?= base_url('admin/fees') ?>" class="rounded-sm border border-stroke bg-white p-7.5 shadow-default transition-all duration-300 hover:shadow-lg dark:border-strokedark dark:bg-boxdark">
                        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                            <i class="fas fa-dollar-sign text-primary text-xl"></i>
                        </div>
                        <div class="mt-4">
                            <h4 class="text-title-md font-bold text-black dark:text-white">
                                Manage Fees
                            </h4>
                            <p class="mt-1 text-sm">Set up and manage fee structures</p>
                        </div>
                    </a>

                    <a href="<?= base_url('admin/reports') ?>" class="rounded-sm border border-stroke bg-white p-7.5 shadow-default transition-all duration-300 hover:shadow-lg dark:border-strokedark dark:bg-boxdark">
                        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                            <i class="fas fa-chart-line text-primary text-xl"></i>
                        </div>
                        <div class="mt-4">
                            <h4 class="text-title-md font-bold text-black dark:text-white">
                                View Reports
                            </h4>
                            <p class="mt-1 text-sm">Generate and view system reports</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Enrollment Chart
    const enrollmentCtx = document.getElementById('enrollmentChart').getContext('2d');

    // Prepare enrollment data
    const enrollmentData = <?= json_encode($charts_data['monthly_admissions'] ?? []) ?>;
    const monthlyData = new Array(12).fill(0);

    // Map enrollment data to months
    enrollmentData.forEach(item => {
        if (item.month && item.count) {
            monthlyData[item.month - 1] = item.count;
        }
    });

    const enrollmentChart = new Chart(enrollmentCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Enrollments',
                data: monthlyData,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Expenses Chart
    const expensesCtx = document.getElementById('expensesChart').getContext('2d');

    // Prepare expenses data
    const expensesData = <?= json_encode($charts_data['monthly_expenses'] ?? []) ?>;
    const expenseLabels = expensesData.map(item => item.month ? 'Month ' + item.month : 'Unknown');
    const expenseValues = expensesData.map(item => parseFloat(item.total) || 0);

    const expensesChart = new Chart(expensesCtx, {
        type: 'doughnut',
        data: {
            labels: expenseLabels,
            datasets: [{
                data: expenseValues,
                backgroundColor: [
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(168, 85, 247, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(236, 72, 153, 0.8)'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
</script>
<?= $this->endSection() ?>
