<?php

namespace App\Models;

class StudentDocModel extends BaseModel
{
    protected $table = 'student_doc';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_id', 'title', 'doc'
    ];

    protected $validationRules = [
        'student_id' => 'required|integer',
        'title' => 'required|min_length[3]|max_length[200]',
        'doc' => 'permit_empty|max_length[200]'
    ];

    protected $validationMessages = [
        'student_id' => [
            'required' => 'Student is required',
            'integer' => 'Invalid student selection'
        ],
        'title' => [
            'required' => 'Document title is required',
            'min_length' => 'Title must be at least 3 characters long',
            'max_length' => 'Title cannot exceed 200 characters'
        ],
        'doc' => [
            'max_length' => 'Document filename cannot exceed 200 characters'
        ]
    ];

    protected $searchableColumns = ['title'];
    protected $orderableColumns = ['id', 'title', 'created_at'];

    /**
     * Get documents with student details
     */
    public function getDocumentsWithStudentDetails($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_doc.*, students.firstname, students.lastname, students.admission_no')
                ->join('students', 'student_doc.student_id = students.id');

        // Apply filters
        if (!empty($filters['student_id'])) {
            $builder->where('student_doc.student_id', $filters['student_id']);
        }

        if (!empty($filters['document_type'])) {
            $builder->like('student_doc.title', $filters['document_type']);
        }

        return $builder->orderBy('student_doc.created_at', 'DESC');
    }

    /**
     * Get student documents
     */
    public function getStudentDocuments($studentId)
    {
        return $this->where('student_id', $studentId)
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Upload document
     */
    public function uploadDocument($data, $file = null)
    {
        try {
            // Handle file upload if provided
            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Generate unique filename
                $fileName = $file->getRandomName();

                // Create upload directory if it doesn't exist
                $uploadPath = WRITEPATH . 'uploads/student_documents/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // Move file to upload directory
                if ($file->move($uploadPath, $fileName)) {
                    $data['doc'] = $fileName;
                } else {
                    return [
                        'success' => false,
                        'message' => 'Failed to upload document file',
                        'errors' => ['doc' => ['File upload failed']]
                    ];
                }
            }

            // Validate data before insertion
            if (!$this->validate($data)) {
                return [
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $this->errors()
                ];
            }

            // Insert record
            $id = $this->insert($data);

            if ($id) {
                return [
                    'success' => true,
                    'message' => 'Document uploaded successfully',
                    'data' => $this->find($id)
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to save document to database',
                    'errors' => ['database' => ['Database insertion failed']]
                ];
            }
        } catch (\Exception $e) {
            // Clean up uploaded file if database insertion fails
            if (isset($fileName) && file_exists(WRITEPATH . 'uploads/student_documents/' . $fileName)) {
                unlink(WRITEPATH . 'uploads/student_documents/' . $fileName);
            }

            return [
                'success' => false,
                'message' => 'An error occurred while uploading the document',
                'errors' => ['exception' => [$e->getMessage()]]
            ];
        }
    }

    /**
     * Update document
     */
    public function updateDocument($id, $data, $file = null)
    {
        $existingRecord = $this->find($id);
        if (!$existingRecord) {
            return [
                'success' => false,
                'message' => 'Document not found',
                'errors' => []
            ];
        }

        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Delete old file if exists
            if (!empty($existingRecord['doc'])) {
                $oldFilePath = WRITEPATH . 'uploads/student_documents/' . $existingRecord['doc'];
                if (file_exists($oldFilePath)) {
                    unlink($oldFilePath);
                }
            }

            // Generate unique filename
            $fileName = $file->getRandomName();
            
            // Create upload directory if it doesn't exist
            $uploadPath = WRITEPATH . 'uploads/student_documents/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            // Move file to upload directory
            if ($file->move($uploadPath, $fileName)) {
                $data['doc'] = $fileName;
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to upload document',
                    'errors' => ['doc' => 'File upload failed']
                ];
            }
        }

        return $this->updateRecord($id, $data);
    }

    /**
     * Delete document with file
     */
    public function deleteDocument($id)
    {
        $record = $this->find($id);
        if (!$record) {
            return [
                'success' => false,
                'message' => 'Document not found',
                'errors' => []
            ];
        }

        // Delete file if exists
        if (!empty($record['doc'])) {
            $filePath = WRITEPATH . 'uploads/student_documents/' . $record['doc'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        // Delete database record
        if ($this->delete($id)) {
            return [
                'success' => true,
                'message' => 'Document deleted successfully',
                'data' => null
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to delete document',
                'errors' => $this->errors()
            ];
        }
    }

    /**
     * Get document file path
     */
    public function getDocumentPath($filename)
    {
        return WRITEPATH . 'uploads/student_documents/' . $filename;
    }

    /**
     * Check if document file exists
     */
    public function documentFileExists($filename)
    {
        return file_exists($this->getDocumentPath($filename));
    }

    /**
     * Get document statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total documents
        $stats['total'] = $this->countAllResults();
        
        // Documents with files
        $stats['with_files'] = $this->where('doc IS NOT NULL')
                                   ->where('doc !=', '')
                                   ->countAllResults();
        
        // Documents without files
        $stats['without_files'] = $stats['total'] - $stats['with_files'];
        
        // Documents by student count
        $stats['students_with_docs'] = $this->db->table($this->table)
                                               ->select('student_id')
                                               ->distinct()
                                               ->countAllResults();
        
        // Most recent uploads (this month)
        $stats['this_month'] = $this->where('MONTH(created_at)', date('m'))
                                   ->where('YEAR(created_at)', date('Y'))
                                   ->countAllResults();
        
        return $stats;
    }

    /**
     * Get common document types
     */
    public function getCommonDocumentTypes()
    {
        return [
            'Birth Certificate',
            'Admission Form',
            'Previous School Certificate',
            'Medical Certificate',
            'Photo ID',
            'Address Proof',
            'Income Certificate',
            'Caste Certificate',
            'Transfer Certificate',
            'Character Certificate',
            'Medical Report',
            'Vaccination Record',
            'Academic Transcript',
            'Fee Receipt',
            'Other'
        ];
    }

    /**
     * Get allowed file extensions
     */
    public function getAllowedExtensions()
    {
        return ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'];
    }

    /**
     * Get max file size (in bytes)
     */
    public function getMaxFileSize()
    {
        return 5 * 1024 * 1024; // 5MB
    }

    /**
     * Validate file upload
     */
    public function validateFileUpload($file)
    {
        $errors = [];

        if (!$file->isValid()) {
            $errors[] = 'Invalid file upload';
            return $errors;
        }

        // Check file size
        if ($file->getSize() > $this->getMaxFileSize()) {
            $errors[] = 'File size cannot exceed 5MB';
        }

        // Check file extension
        $extension = $file->getClientExtension();
        if (!in_array(strtolower($extension), $this->getAllowedExtensions())) {
            $errors[] = 'File type not allowed. Allowed types: ' . implode(', ', $this->getAllowedExtensions());
        }

        return $errors;
    }
}
