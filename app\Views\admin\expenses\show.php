<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-bold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php if (isset($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= esc($breadcrumb['name']) ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= esc($breadcrumb['name']) ?></a></li>
                        <li class="text-gray-500">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Action Buttons -->
<div class="mb-6 flex flex-wrap gap-3">
    <a
        href="<?= base_url($route_prefix) ?>"
        class="inline-flex items-center justify-center rounded-md border border-stroke py-2 px-6 text-center font-medium text-black hover:bg-opacity-90 dark:border-strokedark dark:text-white lg:px-8 xl:px-10"
    >
        <i class="fas fa-arrow-left mr-2"></i>
        Back to List
    </a>
    <a
        href="<?= base_url($route_prefix . '/edit/' . $record['id']) ?>"
        class="inline-flex items-center justify-center rounded-md bg-primary py-2 px-6 text-center font-medium text-white hover:bg-opacity-90 lg:px-8 xl:px-10"
    >
        <i class="fas fa-edit mr-2"></i>
        Edit Expense
    </a>
    <button
        onclick="printExpense()"
        class="inline-flex items-center justify-center rounded-md border border-primary py-2 px-6 text-center font-medium text-primary hover:bg-primary hover:text-white lg:px-8 xl:px-10"
    >
        <i class="fas fa-print mr-2"></i>
        Print
    </button>
</div>

<!-- Expense Details Card -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Expense Details
        </h3>
    </div>
    
    <div class="p-7">
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <!-- Expense Date -->
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                    Expense Date
                </label>
                <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <?= date('F j, Y', strtotime($record['expense_date'])) ?>
                </div>
            </div>

            <!-- Expense Head -->
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                    Expense Head
                </label>
                <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <?= esc($record['expense_head_name'] ?? 'N/A') ?>
                </div>
            </div>

            <!-- Amount -->
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                    Amount
                </label>
                <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <span class="text-lg font-semibold text-primary">
                        <?= 'Rp ' . number_format($record['amount'], 2, ',', '.') ?>
                    </span>
                </div>
            </div>

            <!-- Invoice Number -->
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                    Invoice Number
                </label>
                <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <?= esc($record['invoice_number'] ?? 'N/A') ?>
                </div>
            </div>

            <!-- Status -->
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                    Status
                </label>
                <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <?php
                    $status = $record['status'] ?? 'pending';
                    $statusClass = $status === 'approved' ? 'bg-success' : ($status === 'pending' ? 'bg-warning' : 'bg-danger');
                    $statusText = ucfirst($status);
                    ?>
                    <span class="inline-flex rounded-full <?= $statusClass ?> bg-opacity-10 py-1 px-3 text-sm font-medium text-<?= $status === 'approved' ? 'success' : ($status === 'pending' ? 'warning' : 'danger') ?>">
                        <?= $statusText ?>
                    </span>
                </div>
            </div>

            <!-- Created Date -->
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                    Created Date
                </label>
                <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <?= date('F j, Y g:i A', strtotime($record['created_at'])) ?>
                </div>
            </div>

            <!-- Description -->
            <div class="sm:col-span-2">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                    Description
                </label>
                <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <?= nl2br(esc($record['description'])) ?>
                </div>
            </div>

            <!-- Attachment -->
            <?php if (!empty($record['attachment'])): ?>
                <div class="sm:col-span-2">
                    <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                        Attachment
                    </label>
                    <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-file text-primary text-lg"></i>
                            <div>
                                <a href="<?= base_url('writable/uploads/expenses/' . $record['attachment']) ?>" 
                                   target="_blank" 
                                   class="text-primary hover:underline font-medium">
                                    <?= esc($record['attachment']) ?>
                                </a>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    Click to view/download attachment
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Created By -->
            <?php if (!empty($record['created_by'])): ?>
                <div>
                    <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                        Created By
                    </label>
                    <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                        <?= esc($record['created_by_name'] ?? 'System') ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Last Updated -->
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">
                    Last Updated
                </label>
                <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <?= date('F j, Y g:i A', strtotime($record['updated_at'])) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notes/Comments Section -->
<?php if (!empty($record['notes'])): ?>
<div class="mt-6 rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Notes & Comments
        </h3>
    </div>
    
    <div class="p-7">
        <div class="rounded-md border border-stroke bg-gray py-3 px-4 text-black dark:border-strokedark dark:bg-meta-4 dark:text-white">
            <?= nl2br(esc($record['notes'])) ?>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function printExpense() {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    
    const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Expense Details - <?= esc($record['invoice_number'] ?? 'EXP-' . $record['id']) ?></title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .details { margin-bottom: 20px; }
                .row { display: flex; margin-bottom: 10px; }
                .label { font-weight: bold; width: 150px; }
                .value { flex: 1; }
                .amount { font-size: 18px; font-weight: bold; color: #3b82f6; }
                .status { padding: 5px 10px; border-radius: 15px; font-size: 12px; }
                .status.approved { background: #dcfce7; color: #166534; }
                .status.pending { background: #fef3c7; color: #92400e; }
                .status.rejected { background: #fee2e2; color: #dc2626; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Expense Details</h1>
                <p>Invoice: <?= esc($record['invoice_number'] ?? 'EXP-' . $record['id']) ?></p>
            </div>
            
            <div class="details">
                <div class="row">
                    <div class="label">Expense Date:</div>
                    <div class="value"><?= date('F j, Y', strtotime($record['expense_date'])) ?></div>
                </div>
                <div class="row">
                    <div class="label">Expense Head:</div>
                    <div class="value"><?= esc($record['expense_head_name'] ?? 'N/A') ?></div>
                </div>
                <div class="row">
                    <div class="label">Amount:</div>
                    <div class="value amount">Rp <?= number_format($record['amount'], 2, ',', '.') ?></div>
                </div>
                <div class="row">
                    <div class="label">Status:</div>
                    <div class="value">
                        <span class="status <?= $record['status'] ?? 'pending' ?>">
                            <?= ucfirst($record['status'] ?? 'pending') ?>
                        </span>
                    </div>
                </div>
                <div class="row">
                    <div class="label">Description:</div>
                    <div class="value"><?= nl2br(esc($record['description'])) ?></div>
                </div>
                <div class="row">
                    <div class="label">Created Date:</div>
                    <div class="value"><?= date('F j, Y g:i A', strtotime($record['created_at'])) ?></div>
                </div>
            </div>
            
            <script>
                window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                        window.close();
                    }
                }
            </script>
        </body>
        </html>
    `;
    
    printWindow.document.write(printContent);
    printWindow.document.close();
}
</script>

<?= $this->endSection() ?>
