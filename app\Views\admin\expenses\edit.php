<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-bold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php if (isset($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= esc($breadcrumb['name']) ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= esc($breadcrumb['name']) ?></a></li>
                        <li class="text-gray-500">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Form Card -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Edit Expense Information
        </h3>
    </div>
    
    <div class="p-7">
        <?= form_open_multipart($route_prefix . '/update/' . $record['id'], ['id' => 'expense-form']) ?>
        
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <!-- Expense Date -->
            <div>
                <label class="mb-2.5 block text-black dark:text-white">
                    Expense Date <span class="text-meta-1">*</span>
                </label>
                <input
                    type="date"
                    name="expense_date"
                    value="<?= old('expense_date', $record['expense_date'] ?? '') ?>"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                    required
                />
                <?php if (isset($errors['expense_date'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['expense_date']) ?></p>
                <?php endif; ?>
            </div>

            <!-- Expense Head -->
            <div>
                <label class="mb-2.5 block text-black dark:text-white">
                    Expense Head <span class="text-meta-1">*</span>
                </label>
                <select
                    name="expense_head_id"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                    required
                >
                    <option value="">Select Expense Head</option>
                    <?php if (isset($expense_heads)): ?>
                        <?php foreach ($expense_heads as $id => $name): ?>
                            <option value="<?= $id ?>" <?= (old('expense_head_id', $record['expense_head_id'] ?? '') == $id) ? 'selected' : '' ?>>
                                <?= esc($name) ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
                <?php if (isset($errors['expense_head_id'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['expense_head_id']) ?></p>
                <?php endif; ?>
            </div>

            <!-- Amount -->
            <div>
                <label class="mb-2.5 block text-black dark:text-white">
                    Amount <span class="text-meta-1">*</span>
                </label>
                <input
                    type="number"
                    name="amount"
                    value="<?= old('amount', $record['amount'] ?? '') ?>"
                    step="0.01"
                    min="0"
                    placeholder="Enter amount"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                    required
                />
                <?php if (isset($errors['amount'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['amount']) ?></p>
                <?php endif; ?>
            </div>

            <!-- Invoice Number -->
            <div>
                <label class="mb-2.5 block text-black dark:text-white">
                    Invoice Number
                </label>
                <input
                    type="text"
                    name="invoice_number"
                    value="<?= old('invoice_number', $record['invoice_number'] ?? '') ?>"
                    placeholder="Enter invoice number"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                />
                <?php if (isset($errors['invoice_number'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['invoice_number']) ?></p>
                <?php endif; ?>
            </div>

            <!-- Description -->
            <div class="sm:col-span-2">
                <label class="mb-2.5 block text-black dark:text-white">
                    Description <span class="text-meta-1">*</span>
                </label>
                <textarea
                    name="description"
                    rows="4"
                    placeholder="Enter expense description"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                    required
                ><?= old('description', $record['description'] ?? '') ?></textarea>
                <?php if (isset($errors['description'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['description']) ?></p>
                <?php endif; ?>
            </div>

            <!-- Current Attachment -->
            <?php if (!empty($record['attachment'])): ?>
                <div class="sm:col-span-2">
                    <label class="mb-2.5 block text-black dark:text-white">
                        Current Attachment
                    </label>
                    <div class="flex items-center gap-3 p-3 border border-stroke rounded-md dark:border-strokedark">
                        <i class="fas fa-file text-primary"></i>
                        <a href="<?= base_url('writable/uploads/expenses/' . $record['attachment']) ?>" 
                           target="_blank" 
                           class="text-primary hover:underline">
                            <?= esc($record['attachment']) ?>
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- New Attachment -->
            <div class="sm:col-span-2">
                <label class="mb-2.5 block text-black dark:text-white">
                    <?= !empty($record['attachment']) ? 'Replace Attachment' : 'Attachment' ?>
                </label>
                <input
                    type="file"
                    name="attachment"
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                />
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Supported formats: PDF, JPG, PNG, DOC, DOCX (Max: 5MB)
                    <?= !empty($record['attachment']) ? ' - Leave empty to keep current attachment' : '' ?>
                </p>
                <?php if (isset($errors['attachment'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['attachment']) ?></p>
                <?php endif; ?>
            </div>

            <!-- Status -->
            <div class="sm:col-span-2">
                <label class="mb-2.5 block text-black dark:text-white">
                    Status
                </label>
                <select
                    name="status"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                >
                    <option value="pending" <?= (old('status', $record['status'] ?? 'pending') == 'pending') ? 'selected' : '' ?>>
                        Pending
                    </option>
                    <option value="approved" <?= (old('status', $record['status'] ?? '') == 'approved') ? 'selected' : '' ?>>
                        Approved
                    </option>
                    <option value="rejected" <?= (old('status', $record['status'] ?? '') == 'rejected') ? 'selected' : '' ?>>
                        Rejected
                    </option>
                </select>
                <?php if (isset($errors['status'])): ?>
                    <p class="mt-1 text-sm text-meta-1"><?= esc($errors['status']) ?></p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-end">
            <a
                href="<?= base_url($route_prefix) ?>"
                class="inline-flex items-center justify-center rounded-md border border-stroke py-3 px-6 text-center font-medium text-black hover:bg-opacity-90 dark:border-strokedark dark:text-white lg:px-8 xl:px-10"
            >
                <i class="fas fa-times mr-2"></i>
                Cancel
            </a>
            <button
                type="submit"
                class="inline-flex items-center justify-center rounded-md bg-primary py-3 px-6 text-center font-medium text-white hover:bg-opacity-90 lg:px-8 xl:px-10"
            >
                <i class="fas fa-save mr-2"></i>
                Update Expense
            </button>
        </div>
        
        <?= form_close() ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('expense-form');
    
    form.addEventListener('submit', function(e) {
        // Basic client-side validation
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>

<?= $this->endSection() ?>
