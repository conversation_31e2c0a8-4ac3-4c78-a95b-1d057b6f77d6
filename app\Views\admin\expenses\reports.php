<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-bold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php if (isset($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= esc($breadcrumb['name']) ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= esc($breadcrumb['name']) ?></a></li>
                        <li class="text-gray-500">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Filters Card -->
<div class="mb-6 rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Report Filters
        </h3>
    </div>
    
    <div class="p-7">
        <form id="report-filters" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <!-- Report Type -->
            <div>
                <label class="mb-2.5 block text-black dark:text-white">
                    Report Type
                </label>
                <select
                    id="report-type"
                    name="report_type"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                >
                    <option value="monthly">Monthly Summary</option>
                    <option value="category">By Category</option>
                    <option value="detailed">Detailed Report</option>
                    <option value="yearly">Yearly Summary</option>
                </select>
            </div>

            <!-- Year -->
            <div>
                <label class="mb-2.5 block text-black dark:text-white">
                    Year
                </label>
                <select
                    id="year"
                    name="year"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                >
                    <?php if (isset($years)): ?>
                        <?php foreach ($years as $year => $yearLabel): ?>
                            <option value="<?= $year ?>" <?= $year == date('Y') ? 'selected' : '' ?>>
                                <?= $yearLabel ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <!-- Month -->
            <div>
                <label class="mb-2.5 block text-black dark:text-white">
                    Month
                </label>
                <select
                    id="month"
                    name="month"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                >
                    <option value="">All Months</option>
                    <option value="1">January</option>
                    <option value="2">February</option>
                    <option value="3">March</option>
                    <option value="4">April</option>
                    <option value="5">May</option>
                    <option value="6">June</option>
                    <option value="7">July</option>
                    <option value="8">August</option>
                    <option value="9">September</option>
                    <option value="10">October</option>
                    <option value="11">November</option>
                    <option value="12">December</option>
                </select>
            </div>

            <!-- Expense Head -->
            <div>
                <label class="mb-2.5 block text-black dark:text-white">
                    Expense Head
                </label>
                <select
                    id="expense-head"
                    name="expense_head_id"
                    class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                >
                    <option value="">All Categories</option>
                    <?php if (isset($expense_heads)): ?>
                        <?php foreach ($expense_heads as $id => $name): ?>
                            <option value="<?= $id ?>"><?= esc($name) ?></option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>

            <!-- Action Buttons -->
            <div class="sm:col-span-2 lg:col-span-4 flex flex-wrap gap-3">
                <button
                    type="button"
                    id="generate-report"
                    class="inline-flex items-center justify-center rounded-md bg-primary py-3 px-6 text-center font-medium text-white hover:bg-opacity-90"
                >
                    <i class="fas fa-chart-bar mr-2"></i>
                    Generate Report
                </button>
                <button
                    type="button"
                    id="export-pdf"
                    class="inline-flex items-center justify-center rounded-md border border-danger py-3 px-6 text-center font-medium text-danger hover:bg-danger hover:text-white"
                >
                    <i class="fas fa-file-pdf mr-2"></i>
                    Export PDF
                </button>
                <button
                    type="button"
                    id="export-excel"
                    class="inline-flex items-center justify-center rounded-md border border-success py-3 px-6 text-center font-medium text-success hover:bg-success hover:text-white"
                >
                    <i class="fas fa-file-excel mr-2"></i>
                    Export Excel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Report Results -->
<div id="report-results" class="hidden">
    <!-- Summary Cards -->
    <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                <i class="fas fa-money-bill-wave text-primary text-xl"></i>
            </div>
            <div class="mt-4 flex items-end justify-between">
                <div>
                    <h4 id="total-expenses" class="text-title-md font-bold text-black dark:text-white">
                        Rp 0
                    </h4>
                    <span class="text-sm font-medium">Total Expenses</span>
                </div>
            </div>
        </div>

        <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                <i class="fas fa-receipt text-primary text-xl"></i>
            </div>
            <div class="mt-4 flex items-end justify-between">
                <div>
                    <h4 id="total-transactions" class="text-title-md font-bold text-black dark:text-white">
                        0
                    </h4>
                    <span class="text-sm font-medium">Total Transactions</span>
                </div>
            </div>
        </div>

        <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                <i class="fas fa-chart-line text-primary text-xl"></i>
            </div>
            <div class="mt-4 flex items-end justify-between">
                <div>
                    <h4 id="average-expense" class="text-title-md font-bold text-black dark:text-white">
                        Rp 0
                    </h4>
                    <span class="text-sm font-medium">Average Expense</span>
                </div>
            </div>
        </div>

        <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                <i class="fas fa-tags text-primary text-xl"></i>
            </div>
            <div class="mt-4 flex items-end justify-between">
                <div>
                    <h4 id="top-category" class="text-title-md font-bold text-black dark:text-white">
                        -
                    </h4>
                    <span class="text-sm font-medium">Top Category</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart -->
    <div class="mb-6 rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">
                Expense Chart
            </h3>
        </div>
        <div class="p-7">
            <canvas id="expense-chart" width="400" height="200"></canvas>
        </div>
    </div>

    <!-- Data Table -->
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">
                Report Data
            </h3>
        </div>
        <div class="p-7">
            <div class="max-w-full overflow-x-auto">
                <table id="report-table" class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-2 text-left dark:bg-meta-4">
                            <!-- Table headers will be populated dynamically -->
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Table data will be populated dynamically -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentChart = null;

    // Generate Report
    document.getElementById('generate-report').addEventListener('click', function() {
        const formData = new FormData(document.getElementById('report-filters'));
        
        fetch('<?= base_url($route_prefix . '/generateReport') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayReport(data.data, data.report_type);
                document.getElementById('report-results').classList.remove('hidden');
            } else {
                alert('Failed to generate report: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while generating the report');
        });
    });

    function displayReport(data, reportType) {
        // Update summary cards
        updateSummaryCards(data);
        
        // Update chart
        updateChart(data, reportType);
        
        // Update table
        updateTable(data, reportType);
    }

    function updateSummaryCards(data) {
        const totalExpenses = data.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0);
        const totalTransactions = data.length;
        const averageExpense = totalTransactions > 0 ? totalExpenses / totalTransactions : 0;
        
        document.getElementById('total-expenses').textContent = 'Rp ' + totalExpenses.toLocaleString('id-ID');
        document.getElementById('total-transactions').textContent = totalTransactions.toLocaleString();
        document.getElementById('average-expense').textContent = 'Rp ' + averageExpense.toLocaleString('id-ID');
        
        // Find top category
        const categories = {};
        data.forEach(item => {
            const category = item.expense_head_name || 'Other';
            categories[category] = (categories[category] || 0) + parseFloat(item.amount || 0);
        });
        
        const topCategory = Object.keys(categories).reduce((a, b) => categories[a] > categories[b] ? a : b, '-');
        document.getElementById('top-category').textContent = topCategory;
    }

    function updateChart(data, reportType) {
        const ctx = document.getElementById('expense-chart').getContext('2d');
        
        if (currentChart) {
            currentChart.destroy();
        }

        // Prepare chart data based on report type
        let chartData = {};
        
        if (reportType === 'monthly') {
            // Group by month
            const months = {};
            data.forEach(item => {
                const month = new Date(item.expense_date).toLocaleString('default', { month: 'short' });
                months[month] = (months[month] || 0) + parseFloat(item.amount || 0);
            });
            chartData = {
                labels: Object.keys(months),
                datasets: [{
                    label: 'Monthly Expenses',
                    data: Object.values(months),
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 1
                }]
            };
        } else if (reportType === 'category') {
            // Group by category
            const categories = {};
            data.forEach(item => {
                const category = item.expense_head_name || 'Other';
                categories[category] = (categories[category] || 0) + parseFloat(item.amount || 0);
            });
            chartData = {
                labels: Object.keys(categories),
                datasets: [{
                    label: 'Expenses by Category',
                    data: Object.values(categories),
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.5)',
                        'rgba(16, 185, 129, 0.5)',
                        'rgba(245, 158, 11, 0.5)',
                        'rgba(239, 68, 68, 0.5)',
                        'rgba(139, 92, 246, 0.5)'
                    ],
                    borderWidth: 1
                }]
            };
        }

        if (typeof Chart !== 'undefined') {
            currentChart = new Chart(ctx, {
                type: reportType === 'category' ? 'doughnut' : 'bar',
                data: chartData,
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Expense Report Chart'
                        }
                    }
                }
            });
        }
    }

    function updateTable(data, reportType) {
        const table = document.getElementById('report-table');
        const thead = table.querySelector('thead tr');
        const tbody = table.querySelector('tbody');
        
        // Clear existing content
        thead.innerHTML = '';
        tbody.innerHTML = '';
        
        if (data.length === 0) {
            tbody.innerHTML = '<tr><td colspan="100%" class="text-center py-4">No data found</td></tr>';
            return;
        }
        
        // Create headers based on report type
        let headers = [];
        if (reportType === 'detailed') {
            headers = ['Date', 'Category', 'Description', 'Amount', 'Status'];
        } else if (reportType === 'category') {
            headers = ['Category', 'Total Amount', 'Count', 'Percentage'];
        } else {
            headers = ['Period', 'Total Amount', 'Count'];
        }
        
        headers.forEach(header => {
            const th = document.createElement('th');
            th.className = 'py-4 px-4 font-medium text-black dark:text-white';
            th.textContent = header;
            thead.appendChild(th);
        });
        
        // Populate table data
        data.forEach(item => {
            const tr = document.createElement('tr');
            tr.className = 'border-b border-stroke dark:border-strokedark';
            
            if (reportType === 'detailed') {
                tr.innerHTML = `
                    <td class="py-4 px-4">${new Date(item.expense_date).toLocaleDateString()}</td>
                    <td class="py-4 px-4">${item.expense_head_name || 'N/A'}</td>
                    <td class="py-4 px-4">${item.description || 'N/A'}</td>
                    <td class="py-4 px-4">Rp ${parseFloat(item.amount || 0).toLocaleString('id-ID')}</td>
                    <td class="py-4 px-4">
                        <span class="inline-flex rounded-full bg-${item.status === 'approved' ? 'success' : item.status === 'pending' ? 'warning' : 'danger'} bg-opacity-10 py-1 px-3 text-sm font-medium text-${item.status === 'approved' ? 'success' : item.status === 'pending' ? 'warning' : 'danger'}">
                            ${item.status || 'pending'}
                        </span>
                    </td>
                `;
            }
            
            tbody.appendChild(tr);
        });
    }

    // Export functions
    document.getElementById('export-pdf').addEventListener('click', function() {
        const formData = new FormData(document.getElementById('report-filters'));
        formData.append('format', 'pdf');
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url($route_prefix . '/exportReport') ?>';
        
        for (let [key, value] of formData.entries()) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    });

    document.getElementById('export-excel').addEventListener('click', function() {
        const formData = new FormData(document.getElementById('report-filters'));
        formData.append('format', 'excel');
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url($route_prefix . '/exportReport') ?>';
        
        for (let [key, value] of formData.entries()) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    });
});
</script>

<?= $this->endSection() ?>
