<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use <PERSON>Igniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var list<string>
     */
    protected $helpers = [];

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */
    // protected $session;

    /**
     * @return void
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = service('session');
    }

    /**
     * Get current user data for views
     */
    protected function getCurrentUserData()
    {
        if (!auth()->loggedIn()) {
            return [
                'current_user' => null,
                'user_avatar_url' => 'https://ui-avatars.com/api/?name=Guest&background=6b7280&color=fff&size=48',
                'user_display_name' => 'Guest User',
                'user_role_name' => 'Guest'
            ];
        }

        $user = auth()->user();

        return [
            'current_user' => $user,
            'user_avatar_url' => $this->getUserAvatarUrl($user),
            'user_display_name' => $this->getUserDisplayName($user),
            'user_role_name' => $this->getUserRoleName($user)
        ];
    }

    /**
     * Get user avatar URL
     */
    protected function getUserAvatarUrl($user)
    {
        if (!$user) {
            return 'https://ui-avatars.com/api/?name=Guest&background=6b7280&color=fff&size=48';
        }

        // Check if user has uploaded avatar
        if (!empty($user->avatar) && file_exists(WRITEPATH . 'uploads/avatars/' . $user->avatar)) {
            return base_url('writable/uploads/avatars/' . $user->avatar);
        }

        // Generate avatar from name
        $name = trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? ''));
        if (empty($name)) {
            $name = $user->username ?? $user->email ?? 'User';
        }

        return 'https://ui-avatars.com/api/?name=' . urlencode($name) . '&background=3b82f6&color=fff&size=48';
    }

    /**
     * Get user display name
     */
    protected function getUserDisplayName($user)
    {
        if (!$user) {
            return 'Guest User';
        }

        $name = trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? ''));
        if (!empty($name)) {
            return $name;
        }

        return $user->username ?? $user->email ?? 'User';
    }

    /**
     * Get user role display name
     */
    protected function getUserRoleName($user)
    {
        if (!$user) {
            return 'Guest';
        }

        $groups = $user->getGroups();
        if (empty($groups)) {
            return 'User';
        }

        $roleMap = [
            'superadmin' => 'Super Administrator',
            'admin'      => 'Administrator',
            'teacher'    => 'Teacher',
            'staff'      => 'Staff',
            'parent'     => 'Parent',
            'student'    => 'Student',
        ];

        $primaryRole = $groups[0];
        return $roleMap[$primaryRole] ?? ucfirst($primaryRole);
    }
}
