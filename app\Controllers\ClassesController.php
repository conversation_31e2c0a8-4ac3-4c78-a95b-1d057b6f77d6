<?php

namespace App\Controllers;

use App\Models\ClassesModel;
use App\Models\SectionsModel;

class ClassesController extends BaseCrudController
{
    protected $sectionsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        
        $this->viewPath = 'admin/classes';
        $this->routePrefix = 'admin/classes';
        $this->entityName = 'Class';
        $this->entityNamePlural = 'Classes';
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'sections' => $this->sectionsModel->getForDropdown()
        ];
    }

    /**
     * Process form data before saving
     */
    protected function processFormData($data, $id = null)
    {
        // Set default values
        $data['is_active'] = $data['is_active'] ?? 'yes';

        return $data;
    }

    /**
     * Get class sections
     */
    public function getSections($classId)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $sections = $this->model->getClassSections($classId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $sections
        ]);
    }

    /**
     * Assign sections to class
     */
    public function assignSections($classId)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $sectionIds = $this->request->getPost('section_ids');

        if (empty($sectionIds)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No sections selected'
            ]);
        }

        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // First, remove existing assignments
            $db->table('class_sections')
               ->where('class_id', $classId)
               ->delete();

            // Then add new assignments
            foreach ($sectionIds as $sectionId) {
                $data = [
                    'class_id' => $classId,
                    'section_id' => $sectionId,
                    'is_active' => 'yes'
                ];
                $db->table('class_sections')->insert($data);
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to assign sections'
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Sections assigned successfully'
            ]);

        } catch (\Exception $e) {
            $db->transRollback();
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get class statistics
     */
    public function statistics()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Class sections management page
     */
    public function sections($classId)
    {
        $class = $this->model->find($classId);

        if (!$class) {
            return redirect()->to($this->routePrefix)->with('error', 'Class not found');
        }

        $data = array_merge([
            'title' => 'Manage Sections - ' . $class['class'],
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'class' => $class,
            'assigned_sections' => $this->model->getClassSections($classId),
            'all_sections' => $this->sectionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Sections', 'url' => '']
            ]
        ], $this->getCurrentUserData());

        return view($this->viewPath . '/sections', $data);
    }
}
