<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\Shield\Models\UserModel;
use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Config\OAuth;

class Google<PERSON>uthController extends Controller
{
    protected $userModel;
    protected $oauthConfig;
    protected $googleProvider;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->oauthConfig = new OAuth();
        
        $this->googleProvider = new Google([
            'clientId'     => $this->oauthConfig->google['client_id'],
            'clientSecret' => $this->oauthConfig->google['client_secret'],
            'redirectUri'  => $this->oauthConfig->google['redirect_uri'],
        ]);
    }

    /**
     * Redirect to Google OAuth
     */
    public function redirect()
    {
        try {
            // Check if user is already logged in
            if (auth()->loggedIn()) {
                return redirect()->to('/admin')->with('info', 'You are already logged in. Please log out first if you want to sign in with a different account.');
            }

            // Check if Google OAuth is configured
            if (empty($this->oauthConfig->google['client_id']) || empty($this->oauthConfig->google['client_secret'])) {
                return redirect()->to('/login')->with('error', 'Google OAuth is not configured. Please contact administrator.');
            }

            // Generate state for security
            $state = bin2hex(random_bytes(32));
            session()->set('oauth_state', $state);
            session()->set('oauth_provider', 'google');

            // Store the intended redirect URL
            $redirectUrl = session()->getTempdata('beforeLoginUrl') ?? '/admin';
            session()->set('oauth_redirect_url', $redirectUrl);

            // Get authorization URL
            $authUrl = $this->googleProvider->getAuthorizationUrl([
                'scope' => $this->oauthConfig->google['scopes'],
                'state' => $state,
            ]);

            return redirect()->to($authUrl);

        } catch (\Exception $e) {
            log_message('error', 'Google OAuth redirect error: ' . $e->getMessage());
            return redirect()->to('/login')->with('error', 'Failed to connect to Google. Please try again.');
        }
    }

    /**
     * Handle Google OAuth callback
     */
    public function callback()
    {
        try {
            // Verify state parameter first (before any session modifications)
            $state = $this->request->getGet('state');
            $sessionState = session()->get('oauth_state');

            if (empty($state) || $state !== $sessionState) {
                throw new \Exception('Invalid state parameter');
            }

            // Check if user is already logged in and handle session conflicts
            if (auth()->loggedIn()) {
                // Log out the current user to prevent session conflicts
                auth()->logout();
                log_message('info', 'Logged out existing user before OAuth login to prevent session conflicts');
            }

            // Check for authorization code
            $code = $this->request->getGet('code');
            if (empty($code)) {
                $error = $this->request->getGet('error');
                $errorDescription = $this->request->getGet('error_description');
                throw new \Exception('Authorization failed: ' . ($errorDescription ?? $error ?? 'Unknown error'));
            }

            // Exchange code for access token
            $token = $this->googleProvider->getAccessToken('authorization_code', [
                'code' => $code
            ]);

            // Get user details from Google
            $googleUser = $this->googleProvider->getResourceOwner($token);
            $userData = $googleUser->toArray();

            // Debug: Log the user data received from Google
            log_message('debug', 'Google OAuth user data: ' . json_encode($userData));

            // For debugging: also log to error level so it's more visible
            log_message('error', 'DEBUG - Google OAuth user data: ' . json_encode($userData));

            // Process the user
            $user = $this->processOAuthUser($userData);

            if ($user) {
                // Log the user in
                auth()->login($user);

                // Clear OAuth session data
                session()->remove(['oauth_state', 'oauth_provider']);

                // Get redirect URL
                $redirectUrl = session()->get('oauth_redirect_url') ?? '/admin';
                session()->remove('oauth_redirect_url');

                return redirect()->to($redirectUrl)->with('success', 'Successfully signed in with Google!');
            } else {
                throw new \Exception('Failed to process user account');
            }

        } catch (IdentityProviderException $e) {
            log_message('error', 'Google OAuth provider error: ' . $e->getMessage());
            return redirect()->to('/login')->with('error', 'Google authentication failed. Please try again.');
        } catch (\Exception $e) {
            log_message('error', 'Google OAuth callback error: ' . $e->getMessage());
            return redirect()->to('/login')->with('error', 'Authentication failed: ' . $e->getMessage());
        }
    }

    /**
     * Process OAuth user data
     */
    protected function processOAuthUser(array $userData): ?\CodeIgniter\Shield\Entities\User
    {
        try {
            // Log all available data for debugging
            log_message('debug', 'Processing OAuth user data: ' . json_encode($userData));

            $email = $userData['email'] ?? null;
            $name = $userData['name'] ?? null;
            $googleId = $userData['id'] ?? $userData['sub'] ?? null; // 'sub' is the standard OpenID Connect user ID

            // Provide detailed error message about missing data
            $missingFields = [];
            if (empty($email)) $missingFields[] = 'email';
            if (empty($googleId)) $missingFields[] = 'id/sub';

            if (!empty($missingFields)) {
                $availableFields = implode(', ', array_keys($userData));
                throw new \Exception('Required user data missing from Google: ' . implode(', ', $missingFields) . '. Available fields: ' . $availableFields);
            }

            // Check if user exists by email
            $existingUser = $this->userModel->findByCredentials(['email' => $email]);

            if ($existingUser) {
                // Update user profile with Google data
                $this->updateUserProfile($existingUser, $userData);
                return $existingUser;
            }

            // Create new user if auto-registration is enabled
            if ($this->oauthConfig->allowAutoRegistration) {
                return $this->createOAuthUser($userData);
            } else {
                throw new \Exception('Account not found. Please register first or contact administrator.');
            }

        } catch (\Exception $e) {
            log_message('error', 'Process OAuth user error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create new user from OAuth data
     */
    protected function createOAuthUser(array $userData): ?\CodeIgniter\Shield\Entities\User
    {
        try {
            $email = $userData['email'];
            $name = $userData['name'] ?? '';
            $googleId = $userData['id'] ?? $userData['sub']; // Handle both 'id' and 'sub' fields

            // Generate username from email
            $username = $this->generateUsername($email);

            // Create user data
            $userEntity = new \CodeIgniter\Shield\Entities\User([
                'username'           => $username,
                'email'              => $email,
                'active'             => 1,
                'first_name'         => $userData['given_name'] ?? '',
                'last_name'          => $userData['family_name'] ?? '',
                'phone'              => null, // Google doesn't provide phone by default
                'avatar'             => $userData['picture'] ?? '',
                'google_id'          => $googleId,
                'email_verified_at'  => date('Y-m-d H:i:s'),
                'profile_completed'  => 1, // Mark as completed since we have basic info from Google
            ]);

            // Save user
            $result = $this->userModel->save($userEntity);

            if (!$result) {
                $errors = $this->userModel->errors();
                throw new \Exception('Failed to create user account: ' . implode(', ', $errors));
            }

            // Get the user ID - it might be the result itself or the entity's ID
            $userId = is_numeric($result) ? $result : $userEntity->id;

            if (!$userId) {
                // Try to get the insert ID
                $userId = $this->userModel->getInsertID();
            }

            if (!$userId) {
                throw new \Exception('Failed to get user ID after creation');
            }

            // Get the created user as an entity
            $user = $this->userModel->find($userId);

            if (!$user) {
                throw new \Exception('Failed to retrieve created user with ID: ' . $userId);
            }

            // Ensure we have a User entity object
            if (is_array($user)) {
                $user = new \CodeIgniter\Shield\Entities\User($user);
                $user->id = $userId; // Make sure ID is set
            }

            // Debug: Log user object type and structure
            log_message('debug', 'User object type: ' . gettype($user) . ', class: ' . (is_object($user) ? get_class($user) : 'not object'));

            // Add default role
            try {
                $user->addGroup($this->oauthConfig->defaultRole);
                log_message('info', 'Added role "' . $this->oauthConfig->defaultRole . '" to user ID: ' . $userId);
            } catch (\Exception $e) {
                log_message('error', 'Failed to add role to user: ' . $e->getMessage());
                // Don't throw here, user creation was successful
            }

            // Create identity for OAuth (email identity without password)
            try {
                // Check if identity already exists
                $identityModel = model('CodeIgniter\Shield\Models\UserIdentityModel');
                $existingIdentity = $identityModel->where('user_id', $userId)
                                                  ->where('type', 'email_password')
                                                  ->first();

                if (!$existingIdentity) {
                    $user->createEmailIdentity([
                        'email'    => $email,
                        'password' => null, // OAuth users don't have passwords initially
                    ]);
                    log_message('info', 'Created email identity for OAuth user ID: ' . $userId);
                }
            } catch (\Exception $e) {
                log_message('error', 'Failed to create email identity: ' . $e->getMessage());
                // Don't throw here, user creation was successful
            }

            return $user;

        } catch (\Exception $e) {
            log_message('error', 'Create OAuth user error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update user profile with Google OAuth data
     */
    protected function updateUserProfile($user, array $userData): void
    {
        try {
            $updateData = [];

            // Update Google ID if not set
            if (empty($user->google_id)) {
                $updateData['google_id'] = $userData['id'] ?? $userData['sub'] ?? null;
            }

            // Update first name if not set or different
            $googleFirstName = $userData['given_name'] ?? '';
            if (empty($user->first_name) || $user->first_name !== $googleFirstName) {
                $updateData['first_name'] = $googleFirstName;
            }

            // Update last name if not set or different
            $googleLastName = $userData['family_name'] ?? '';
            if (empty($user->last_name) || $user->last_name !== $googleLastName) {
                $updateData['last_name'] = $googleLastName;
            }

            // Update avatar if not set or different
            $googleAvatar = $userData['picture'] ?? '';
            if (empty($user->avatar) || $user->avatar !== $googleAvatar) {
                $updateData['avatar'] = $googleAvatar;
            }

            // Update email verification if not verified
            if (empty($user->email_verified_at) && ($userData['email_verified'] ?? false)) {
                $updateData['email_verified_at'] = date('Y-m-d H:i:s');
            }

            // Mark profile as completed if we have basic info
            if (empty($user->profile_completed) && !empty($googleFirstName) && !empty($googleLastName)) {
                $updateData['profile_completed'] = 1;
            }

            // Update last active
            $updateData['last_active'] = date('Y-m-d H:i:s');

            // Only update if there are changes
            if (!empty($updateData)) {
                $this->userModel->update($user->id, $updateData);
                log_message('info', 'Updated user profile for user ID: ' . $user->id . ' with Google OAuth data');
            }

        } catch (\Exception $e) {
            log_message('error', 'Update user profile error: ' . $e->getMessage());
        }
    }

    /**
     * Update user's Google ID (legacy method - kept for compatibility)
     */
    protected function updateUserGoogleId($user, string $googleId): void
    {
        try {
            if (empty($user->google_id)) {
                $this->userModel->update($user->id, ['google_id' => $googleId]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Update Google ID error: ' . $e->getMessage());
        }
    }

    /**
     * Generate unique username from email
     */
    protected function generateUsername(string $email): string
    {
        $baseUsername = strstr($email, '@', true);
        $baseUsername = preg_replace('/[^a-zA-Z0-9]/', '', $baseUsername);
        
        $username = $baseUsername;
        $counter = 1;

        // Ensure username is unique
        while ($this->userModel->where('username', $username)->first()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }
}
