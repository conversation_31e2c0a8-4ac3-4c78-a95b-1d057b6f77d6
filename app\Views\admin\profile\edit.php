<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-bold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                <?php if ($index === count($breadcrumbs) - 1): ?>
                    <li class="text-primary"><?= esc($breadcrumb['name']) ?></li>
                <?php else: ?>
                    <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= esc($breadcrumb['name']) ?></a></li>
                    <li class="text-gray-500">/</li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>
</div>

<!-- Edit Profile Form -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">
            Edit Profile Information
        </h3>
    </div>
    
    <div class="p-7">
        <?= form_open('admin/profile/update', ['class' => 'space-y-6']) ?>
        
        <div class="mb-8 flex flex-col gap-6 xl:flex-row">
            <!-- Profile Picture Section -->
            <div class="w-full xl:w-1/3">
                <div class="text-center">
                    <div class="mx-auto mb-4 h-32 w-32 rounded-full overflow-hidden border-4 border-primary">
                        <img src="<?= esc($user_avatar_url) ?>" 
                             alt="<?= esc($user_display_name) ?>" 
                             class="h-full w-full object-cover">
                    </div>
                    <h4 class="mb-1.5 text-xl font-semibold text-black dark:text-white">
                        Current Profile Picture
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        Avatar is automatically generated from your name
                    </p>
                </div>
            </div>
            
            <!-- Form Fields -->
            <div class="w-full xl:w-2/3">
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- First Name -->
                    <div>
                        <label class="mb-2.5 block text-black dark:text-white">
                            First Name <span class="text-meta-1">*</span>
                        </label>
                        <input
                            type="text"
                            name="first_name"
                            value="<?= old('first_name', $user->first_name ?? '') ?>"
                            placeholder="Enter your first name"
                            class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                            required
                        />
                        <?php if (isset($errors['first_name'])): ?>
                            <p class="mt-1 text-sm text-meta-1"><?= esc($errors['first_name']) ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label class="mb-2.5 block text-black dark:text-white">
                            Last Name <span class="text-meta-1">*</span>
                        </label>
                        <input
                            type="text"
                            name="last_name"
                            value="<?= old('last_name', $user->last_name ?? '') ?>"
                            placeholder="Enter your last name"
                            class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                            required
                        />
                        <?php if (isset($errors['last_name'])): ?>
                            <p class="mt-1 text-sm text-meta-1"><?= esc($errors['last_name']) ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Email -->
                    <div>
                        <label class="mb-2.5 block text-black dark:text-white">
                            Email Address <span class="text-meta-1">*</span>
                        </label>
                        <input
                            type="email"
                            name="email"
                            value="<?= old('email', $user->email ?? '') ?>"
                            placeholder="Enter your email address"
                            class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                            required
                        />
                        <?php if (isset($errors['email'])): ?>
                            <p class="mt-1 text-sm text-meta-1"><?= esc($errors['email']) ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label class="mb-2.5 block text-black dark:text-white">
                            Phone Number
                        </label>
                        <input
                            type="tel"
                            name="phone"
                            value="<?= old('phone', $user->phone ?? '') ?>"
                            placeholder="Enter your phone number"
                            class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                        />
                        <?php if (isset($errors['phone'])): ?>
                            <p class="mt-1 text-sm text-meta-1"><?= esc($errors['phone']) ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Username (Read-only) -->
                    <div>
                        <label class="mb-2.5 block text-black dark:text-white">
                            Username
                        </label>
                        <input
                            type="text"
                            value="<?= esc($user->username ?? 'Not set') ?>"
                            class="w-full rounded border-[1.5px] border-stroke bg-gray py-3 px-5 font-medium outline-none transition disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input"
                            disabled
                        />
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Username cannot be changed</p>
                    </div>

                    <!-- Account Status (Read-only) -->
                    <div>
                        <label class="mb-2.5 block text-black dark:text-white">
                            Account Status
                        </label>
                        <div class="w-full rounded border-[1.5px] border-stroke bg-gray py-3 px-5 font-medium dark:border-form-strokedark dark:bg-form-input">
                            <span class="inline-flex rounded-full <?= $user->active ? 'bg-success bg-opacity-10 text-success' : 'bg-danger bg-opacity-10 text-danger' ?> py-1 px-3 text-sm font-medium">
                                <?= $user->active ? 'Active' : 'Inactive' ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex flex-col gap-4 sm:flex-row sm:justify-end">
                    <a href="<?= base_url('admin/profile') ?>" 
                       class="inline-flex items-center justify-center rounded-md border border-stroke py-3 px-6 text-center font-medium text-black hover:bg-opacity-90 dark:border-strokedark dark:text-white lg:px-8 xl:px-10">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </a>
                    <button
                        type="submit"
                        class="inline-flex items-center justify-center rounded-md bg-primary py-3 px-6 text-center font-medium text-white hover:bg-opacity-90 lg:px-8 xl:px-10"
                    >
                        <i class="fas fa-save mr-2"></i>
                        Update Profile
                    </button>
                </div>
            </div>
        </div>
        
        <?= form_close() ?>
    </div>
</div>

<?= $this->endSection() ?>
