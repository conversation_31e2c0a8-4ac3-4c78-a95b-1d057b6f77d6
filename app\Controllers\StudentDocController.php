<?php

namespace App\Controllers;

use App\Models\StudentDocModel;
use App\Models\StudentsModel;

class StudentDocController extends BaseCrudController
{
    protected $studentsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentDocModel();
        $this->studentsModel = new StudentsModel();
        
        $this->viewPath = 'admin/student_apps/documents';
        $this->routePrefix = 'admin/student-apps/documents';
        $this->entityName = 'Student Document';
        $this->entityNamePlural = 'Student Documents';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'students' => $this->studentsModel->getForDropdown(),
            'document_types' => $this->model->getCommonDocumentTypes(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'students' => $this->studentsModel->where('is_active', 'yes')->orderBy('firstname', 'ASC')->findAll(),
            'document_types' => $this->model->getCommonDocumentTypes(),
            'allowed_extensions' => $this->model->getAllowedExtensions(),
            'max_file_size' => $this->model->getMaxFileSize()
        ];
    }

    /**
     * Process form data before saving
     */
    protected function processFormData($data, $id = null)
    {
        // Remove any fields that are not in the allowed fields
        $allowedFields = ['student_id', 'title'];
        $processedData = [];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $processedData[$field] = $data[$field];
            }
        }

        return $processedData;
    }

    /**
     * Store new record
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        try {
            $data = $this->request->getPost();
            $file = $this->request->getFile('doc_file');

            // Debug logging
            log_message('debug', 'StudentDoc Store - POST data: ' . json_encode($data));
            log_message('debug', 'StudentDoc Store - File info: ' . ($file ? $file->getName() : 'No file'));

            // Validate required fields
            if (empty($data['student_id'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Student selection is required',
                    'errors' => ['student_id' => ['Student is required']]
                ]);
            }

            if (empty($data['title'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Document title is required',
                    'errors' => ['title' => ['Document title is required']]
                ]);
            }

            // Validate file if uploaded
            if ($file && $file->isValid()) {
                $fileErrors = $this->model->validateFileUpload($file);
                if (!empty($fileErrors)) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'File validation failed',
                        'errors' => ['doc_file' => $fileErrors]
                    ]);
                }
            }

            // Process form data
            $data = $this->processFormData($data);

            $result = $this->model->uploadDocument($data, $file);

            if ($result['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => $this->entityName . ' uploaded successfully',
                    'data' => $result['data']
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to upload ' . strtolower($this->entityName),
                    'errors' => $result['errors']
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'StudentDoc Store Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while uploading the document',
                'errors' => ['general' => [$e->getMessage()]]
            ]);
        }
    }

    /**
     * Update record
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $file = $this->request->getFile('doc_file');

        // Validate file if uploaded
        if ($file && $file->isValid()) {
            $fileErrors = $this->model->validateFileUpload($file);
            if (!empty($fileErrors)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'File validation failed',
                    'errors' => ['doc_file' => $fileErrors]
                ]);
            }
        }

        // Process form data
        $data = $this->processFormData($data, $id);

        $result = $this->model->updateDocument($id, $data, $file);

        if ($result['success']) {
            return $this->response->setJSON([
                'success' => true,
                'message' => $this->entityName . ' updated successfully',
                'data' => $result['data']
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update ' . strtolower($this->entityName),
                'errors' => $result['errors']
            ]);
        }
    }

    /**
     * Delete record
     */
    public function delete($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->deleteDocument($id);

        return $this->response->setJSON($result);
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $filters = [
            'student_id' => $this->request->getPost('student_id'),
            'document_type' => $this->request->getPost('document_type')
        ];

        $builder = $this->model->getDocumentsWithStudentDetails($filters);

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('students.firstname', $searchValue)
                   ->orLike('students.lastname', $searchValue)
                   ->orLike('students.admission_no', $searchValue)
                   ->orLike('student_doc.title', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'firstname', 'title', 'created_at'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->get()->getResultArray();

        $data = [];
        foreach ($records as $record) {
            $hasFile = !empty($record['doc']) && $this->model->documentFileExists($record['doc']);
            $actions = $this->getActionButtons($record, $hasFile);

            $data[] = [
                'id' => $record['id'],
                'student_name' => $record['firstname'] . ' ' . $record['lastname'],
                'admission_no' => $record['admission_no'],
                'title' => $record['title'],
                'has_file' => $hasFile ? '<span class="text-success"><i class="fas fa-check"></i> Yes</span>' : '<span class="text-danger"><i class="fas fa-times"></i> No</span>',
                'created_at' => date('M j, Y', strtotime($record['created_at'])),
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Download document
     */
    public function download($id)
    {
        $record = $this->model->find($id);
        
        if (!$record) {
            return redirect()->back()->with('error', 'Document not found');
        }

        if (empty($record['doc'])) {
            return redirect()->back()->with('error', 'No file attached to this document');
        }

        $filePath = $this->model->getDocumentPath($record['doc']);
        
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'File not found on server');
        }

        return $this->response->download($filePath, null);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record, $hasFile = false)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        if ($hasFile) {
            $buttons .= '<a href="' . base_url($this->routePrefix . '/download/' . $record['id']) . '" class="hover:text-success" title="Download">
                            <i class="fas fa-download"></i>
                        </a>';
        }

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
