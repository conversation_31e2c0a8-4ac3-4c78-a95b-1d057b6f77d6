<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-bold text-black dark:text-white">
        <?= $title ?>
    </h2>
    <nav>
        <ol class="flex items-center gap-2">
            <?php if (isset($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= esc($breadcrumb['name']) ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= esc($breadcrumb['name']) ?></a></li>
                        <li class="text-gray-500">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Action Buttons -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <div class="flex flex-wrap gap-3">
        <button
            id="add-new-btn"
            class="inline-flex items-center justify-center rounded-md bg-primary py-2 px-6 text-center font-medium text-white hover:bg-opacity-90 lg:px-8 xl:px-10"
        >
            <i class="fas fa-plus mr-2"></i>
            Add New Expense
        </button>
        <button
            id="bulk-delete-btn"
            class="inline-flex items-center justify-center rounded-md border border-danger py-2 px-6 text-center font-medium text-danger hover:bg-danger hover:text-white lg:px-8 xl:px-10"
            style="display: none;"
        >
            <i class="fas fa-trash mr-2"></i>
            Delete Selected
        </button>
    </div>
    <div class="flex flex-wrap gap-3">
        <?php if (isset($route_prefix)): ?>
            <a
                href="<?= base_url($route_prefix . '/reports') ?>"
                class="inline-flex items-center justify-center rounded-md border border-primary py-2 px-6 text-center font-medium text-primary hover:bg-primary hover:text-white lg:px-8 xl:px-10"
            >
                <i class="fas fa-chart-bar mr-2"></i>
                Reports
            </a>
        <?php endif; ?>
        <div class="relative">
            <button
                id="export-dropdown-btn"
                class="inline-flex items-center justify-center rounded-md border border-stroke py-2 px-6 text-center font-medium text-black hover:bg-gray-2 dark:border-strokedark dark:text-white dark:hover:bg-meta-4 lg:px-8 xl:px-10"
            >
                <i class="fas fa-download mr-2"></i>
                Export
                <i class="fas fa-chevron-down ml-2"></i>
            </button>
            <div id="export-dropdown" class="absolute right-0 mt-2 w-48 rounded-md border border-stroke bg-white shadow-lg dark:border-strokedark dark:bg-boxdark" style="display: none;">
                <div class="py-1">
                    <?php if (isset($route_prefix)): ?>
                        <a href="<?= base_url($route_prefix . '/export/csv') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-meta-4">
                            <i class="fas fa-file-csv mr-2"></i>Export as CSV
                        </a>
                        <a href="<?= base_url($route_prefix . '/export/excel') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-meta-4">
                            <i class="fas fa-file-excel mr-2"></i>Export as Excel
                        </a>
                        <a href="<?= base_url($route_prefix . '/export/pdf') ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-meta-4">
                            <i class="fas fa-file-pdf mr-2"></i>Export as PDF
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1">
    <div class="max-w-full overflow-x-auto">
        <table id="expenses-table" class="w-full table-auto">
            <thead>
                <tr class="bg-gray-2 text-left dark:bg-meta-4">
                    <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">
                        <input type="checkbox" id="select-all" class="rounded border-gray-300">
                    </th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white xl:pl-11">
                        Date
                    </th>
                    <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">
                        Expense Head
                    </th>
                    <th class="min-w-[200px] py-4 px-4 font-medium text-black dark:text-white">
                        Description
                    </th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">
                        Amount
                    </th>
                    <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">
                        Invoice No
                    </th>
                    <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">
                        Status
                    </th>
                    <th class="py-4 px-4 font-medium text-black dark:text-white">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody>
                <!-- Data will be loaded via AJAX -->
            </tbody>
        </table>
    </div>
</div>

<!-- Create/Edit Modal -->
<div id="expense-modal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="mx-4 w-full max-w-2xl rounded-lg bg-white p-6 dark:bg-boxdark">
        <div class="mb-4 flex items-center justify-between">
            <h3 id="modal-title" class="text-xl font-semibold text-black dark:text-white">Add New Expense</h3>
            <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="expense-form">
            <input type="hidden" id="expense-id" name="id">

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <!-- Expense Date -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">
                        Expense Date <span class="text-meta-1">*</span>
                    </label>
                    <input
                        type="date"
                        id="expense-date"
                        name="expense_date"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                        required
                    />
                </div>

                <!-- Expense Head -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">
                        Expense Head <span class="text-meta-1">*</span>
                    </label>
                    <select
                        id="expense-head"
                        name="expense_head_id"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                        required
                    >
                        <option value="">Select Expense Head</option>
                        <?php if (isset($expense_heads)): ?>
                            <?php foreach ($expense_heads as $id => $name): ?>
                                <option value="<?= $id ?>"><?= esc($name) ?></option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Amount -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">
                        Amount <span class="text-meta-1">*</span>
                    </label>
                    <input
                        type="number"
                        id="amount"
                        name="amount"
                        step="0.01"
                        min="0"
                        placeholder="Enter amount"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                        required
                    />
                </div>

                <!-- Invoice Number -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">
                        Invoice Number
                    </label>
                    <input
                        type="text"
                        id="invoice-number"
                        name="invoice_number"
                        placeholder="Enter invoice number"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                    />
                </div>

                <!-- Description -->
                <div class="sm:col-span-2">
                    <label class="mb-2.5 block text-black dark:text-white">
                        Description <span class="text-meta-1">*</span>
                    </label>
                    <textarea
                        id="description"
                        name="description"
                        rows="4"
                        placeholder="Enter expense description"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                        required
                    ></textarea>
                </div>

                <!-- Attachment -->
                <div class="sm:col-span-2">
                    <label class="mb-2.5 block text-black dark:text-white">
                        Attachment
                    </label>
                    <input
                        type="file"
                        id="attachment"
                        name="attachment"
                        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                        class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
                    />
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        Supported formats: PDF, JPG, PNG, DOC, DOCX (Max: 5MB)
                    </p>
                </div>
            </div>

            <div class="mt-6 flex justify-end gap-4">
                <button
                    type="button"
                    id="cancel-btn"
                    class="inline-flex items-center justify-center rounded-md border border-stroke py-3 px-6 text-center font-medium text-black hover:bg-opacity-90 dark:border-strokedark dark:text-white"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    id="submit-btn"
                    class="inline-flex items-center justify-center rounded-md bg-primary py-3 px-6 text-center font-medium text-white hover:bg-opacity-90"
                >
                    <i class="fas fa-save mr-2"></i>
                    Save Expense
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable if available
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        const table = $('#expenses-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '<?= base_url($route_prefix . '/getData') ?>',
                type: 'POST'
            },
            columns: [
                { data: 'id', orderable: false, searchable: false, render: function(data) {
                    return `<input type="checkbox" class="row-checkbox" value="${data}">`;
                }},
                { data: 'expense_date' },
                { data: 'expense_head_name' },
                { data: 'description' },
                { data: 'amount', render: function(data) {
                    return new Intl.NumberFormat('id-ID', {
                        style: 'currency',
                        currency: 'IDR'
                    }).format(data);
                }},
                { data: 'invoice_number' },
                { data: 'status', render: function(data) {
                    const statusClass = data === 'approved' ? 'bg-success' : data === 'pending' ? 'bg-warning' : 'bg-danger';
                    return `<span class="inline-flex rounded-full ${statusClass} bg-opacity-10 py-1 px-3 text-sm font-medium text-${data === 'approved' ? 'success' : data === 'pending' ? 'warning' : 'danger'}">${data}</span>`;
                }},
                { data: 'id', orderable: false, searchable: false, render: function(data) {
                    return `
                        <div class="flex items-center space-x-3.5">
                            <button class="hover:text-primary edit-btn" data-id="${data}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="hover:text-primary view-btn" data-id="${data}">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="hover:text-danger delete-btn" data-id="${data}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }}
            ],
            order: [[1, 'desc']],
            pageLength: 25,
            responsive: true
        });

        // Edit expense
        $(document).on('click', '.edit-btn', function() {
            const id = $(this).data('id');

            fetch(`<?= base_url($route_prefix) ?>/edit/${id}`)
            .then(response => response.text())
            .then(html => {
                // This would need to be implemented to return JSON data instead
                // For now, we'll show the modal with empty form
                modalTitle.textContent = 'Edit Expense';
                submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Update Expense';
                document.getElementById('expense-id').value = id;
                modal.classList.remove('hidden');
                modal.classList.add('flex');
            });
        });

        // Delete expense
        $(document).on('click', '.delete-btn', function() {
            const id = $(this).data('id');

            if (confirm('Are you sure you want to delete this expense?')) {
                fetch(`<?= base_url($route_prefix) ?>/delete/${id}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        table.ajax.reload();
                        showToast('success', data.message);
                    } else {
                        showToast('error', data.message);
                    }
                });
            }
        });

        // Individual checkbox change
        $(document).on('change', '.row-checkbox', function() {
            toggleBulkDeleteBtn();
        });
    }

    // Modal and form handling
    const modal = document.getElementById('expense-modal');
    const form = document.getElementById('expense-form');
    const modalTitle = document.getElementById('modal-title');
    const submitBtn = document.getElementById('submit-btn');

    // Add new expense
    document.getElementById('add-new-btn').addEventListener('click', function() {
        modalTitle.textContent = 'Add New Expense';
        submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save Expense';
        form.reset();
        document.getElementById('expense-id').value = '';
        document.getElementById('expense-date').value = new Date().toISOString().split('T')[0];
        modal.classList.remove('hidden');
        modal.classList.add('flex');
    });

    // Close modal
    function closeModal() {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    }

    document.getElementById('close-modal').addEventListener('click', closeModal);
    document.getElementById('cancel-btn').addEventListener('click', closeModal);

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const expenseId = document.getElementById('expense-id').value;
        const url = expenseId ?
            `<?= base_url($route_prefix) ?>/update/${expenseId}` :
            `<?= base_url($route_prefix) ?>/store`;

        fetch(url, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeModal();
                if (typeof table !== 'undefined') {
                    table.ajax.reload();
                } else {
                    location.reload();
                }
                showToast('success', data.message);
            } else {
                showToast('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('error', 'An error occurred while saving the expense');
        });
    });

    // Select all checkbox
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        toggleBulkDeleteBtn();
    });

    function toggleBulkDeleteBtn() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        const bulkDeleteBtn = document.getElementById('bulk-delete-btn');

        if (checkedBoxes.length > 0) {
            bulkDeleteBtn.style.display = 'inline-flex';
        } else {
            bulkDeleteBtn.style.display = 'none';
        }
    }

    // Bulk delete
    document.getElementById('bulk-delete-btn').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        const ids = Array.from(checkedBoxes).map(cb => cb.value);

        if (ids.length === 0) {
            showToast('warning', 'Please select expenses to delete');
            return;
        }

        if (confirm(`Are you sure you want to delete ${ids.length} expense(s)?`)) {
            fetch(`<?= base_url($route_prefix) ?>/bulkDelete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ ids: ids })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (typeof table !== 'undefined') {
                        table.ajax.reload();
                    } else {
                        location.reload();
                    }
                    document.getElementById('select-all').checked = false;
                    toggleBulkDeleteBtn();
                    showToast('success', data.message);
                } else {
                    showToast('error', data.message);
                }
            });
        }
    });

    // Export dropdown
    document.getElementById('export-dropdown-btn').addEventListener('click', function() {
        const dropdown = document.getElementById('export-dropdown');
        dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        const dropdown = document.getElementById('export-dropdown');
        const button = document.getElementById('export-dropdown-btn');

        if (!button.contains(e.target) && !dropdown.contains(e.target)) {
            dropdown.style.display = 'none';
        }
    });

    // Toast notification function
    function showToast(type, message) {
        // Simple toast implementation
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 p-4 rounded-md text-white ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        toast.textContent = message;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
});
</script>

<?= $this->endSection() ?>